services:
  - type: web
    name: dashboard-flotta
    env: python
    region: frankfurt  # Europa per performance migliori in Italia
    plan: free
    buildCommand: pip install -r requirements.txt
    startCommand: streamlit run streamlit_app.py --server.port=$PORT --server.address=0.0.0.0 --server.headless=true
    healthCheckPath: /
    envVars:
      - key: STREAMLIT_SERVER_HEADLESS
        value: "true"
      - key: STREAMLIT_SERVER_ENABLE_CORS
        value: "false"
      - key: STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION
        value: "false"
      - key: STREAMLIT_SERVER_FILE_WATCHER_TYPE
        value: "none"
      - key: STREAMLIT_BROWSER_GATHER_USAGE_STATS
        value: "false"
      - key: STREAMLIT_THEME_PRIMARY_COLOR
        value: "#1f77b4"
      - key: STREAMLIT_THEME_BACKGROUND_COLOR
        value: "#ffffff"
      - key: STREAMLIT_THEME_SECONDARY_BACKGROUND_COLOR
        value: "#f0f2f6"
      - key: STREAMLIT_THEME_TEXT_COLOR
        value: "#262730"
    autoDeploy: true
    disk:
      name: dashboard-data
      mountPath: /opt/render/project/data
      sizeGB: 1
