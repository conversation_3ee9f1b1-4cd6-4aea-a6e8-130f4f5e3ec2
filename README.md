# 🚗 Fleet Management Dashboard

A modern, responsive dashboard for vehicle fleet management built with Streamlit and Plotly. This application provides comprehensive analytics and data management capabilities for the Italian vehicle fleet registry.

## ✨ Features

### 🎯 Must-Have Features
- **Global Filters**: Multi-select dropdowns for categories, groups, and status with date range pickers
- **KPI Dashboard**: Real-time metrics including total vehicles, monthly revenue, and expiring leases
- **Interactive Data Table**: Sortable, paginated table with inline editing capabilities
- **Interactive Charts**: Auto-updating Plotly visualizations including:
  - Vehicle distribution by category
  - Revenue comparison charts
  - Lease timeline analysis
  - Status distribution
  - Location analytics
- **Data Export**: Export filtered data as CSV
- **Save Changes**: Write edited data back to the original CSV file

### 🎨 UI/UX
- **Modern Design**: Clean, responsive interface with gradient styling
- **Dark Mode Ready**: Built with TailwindCSS principles
- **Professional Layout**: Organized tabs and sections for optimal user experience

## 🚀 Quick Start

### Prerequisites
- Python 3.12+
- pip package manager

### Installation

1. **Clone or download the project**
   ```bash
   cd DASH-VIV
   ```

2. **Create a virtual environment (recommended)**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Verify CSV file location**
   - Ensure `RegistroFLOTTA_Master.csv` is in the project root directory
   - The file should be UTF-8-SIG encoded with comma separators

### Running the Application

```bash
streamlit run streamlit_app.py
```

The dashboard will open in your default web browser at `http://localhost:8501`

## 📊 Dataset Information

The dashboard works with the Italian vehicle fleet registry data (`RegistroFLOTTA_Master.csv`) containing:

### Key Data Fields
- **Vehicle Information**: ACRISS codes, categories, models, transmission types, license plates
- **Rental Details**: Acquisition type (RENT/LEASING), duration, included kilometers, dates
- **Financial Data**: Monthly fees (taxable and with VAT), insurance coverage
- **Location Data**: Operating locations/sites (PARCO/SEDE)
- **Status Information**: Current vehicle status, return dates, notes

### Data Categories
- **Economy vehicles** (EDAR): Compact cars like Toyota Yaris
- **Large vehicles** (IFAR/IFMR): SUVs like Peugeot 2008, Citroen C3 Aircross
- **Premium vehicles** (PFAR): High-end SUVs like BMW X1
- **Vans** (FVMR/PVAR): Multi-passenger vehicles and cargo vans
- **Commercial vehicles** (K1): Medium-sized delivery vans

## 🏗️ Project Structure

```
DASH-VIV/
├── streamlit_app.py          # Main application entry point
├── requirements.txt          # Python dependencies
├── README.md                # This file
├── CLAUDE.md               # Project instructions
├── RegistroFLOTTA_Master.csv # Dataset (not included in repo)
└── src/
    ├── data_handler.py      # CSV operations and filtering
    └── charts.py           # Plotly chart generation
```

## 🔧 Technical Details

### Built With
- **Streamlit 1.28.1**: Web application framework
- **Pandas 2.1.3**: Data manipulation and analysis
- **Plotly 5.17.0**: Interactive visualization library
- **streamlit-aggrid 0.3.4**: Advanced data grid component
- **NumPy 1.24.3**: Numerical computing

### Architecture
- **Modular Design**: Separated concerns with dedicated modules
- **Type Hints**: Full type annotations for better code maintainability
- **Error Handling**: Comprehensive error handling and user feedback
- **Performance**: Efficient data loading with Streamlit caching
- **Responsive**: Mobile-friendly design with flexible layouts

## 📈 Usage Guide

### 1. Global Filters
Use the sidebar to filter your data:
- **Categories**: Select vehicle categories (ECONOMIA, LARGE, etc.)
- **Groups**: Filter by vehicle groups (ECONOMY, FURGONE, etc.)
- **Status**: Choose vehicle status (Attivo, etc.)
- **Date Ranges**: Set date ranges for start and end dates

### 2. KPI Dashboard
Monitor key metrics at the top:
- **Total Vehicles**: Count of all vehicles in current filter
- **Active Vehicles**: Count of vehicles with "Attivo" status
- **Monthly Revenue**: Sum of CANONE IMPONIBILE
- **Expiring Soon**: Vehicles with leases ending within 90 days (red alert)

### 3. Data Table
- **Sort**: Click column headers to sort
- **Edit**: Click cells to edit values inline
- **Save**: Click "Save Changes" to write back to CSV
- **Export**: Download filtered data as CSV

### 4. Analytics Charts
- **Vehicle Distribution**: Bar chart showing vehicles by category
- **Revenue Comparison**: Stacked bars comparing taxable vs. total revenue
- **Lease Timeline**: Line chart showing lease endings by month
- **Status Distribution**: Pie chart of vehicle statuses
- **Location Analysis**: Top 10 locations by vehicle count

## 🛠️ Development

### Code Quality
- **Type Hints**: All functions include proper type annotations
- **Docstrings**: Comprehensive documentation for all classes and methods
- **Error Handling**: Robust error handling with user-friendly messages
- **Modular Architecture**: Clean separation of concerns

### Extending the Dashboard
To add new features:
1. **New Charts**: Add methods to `src/charts.py`
2. **Data Operations**: Extend `src/data_handler.py`
3. **UI Components**: Modify `streamlit_app.py`

## 📝 License

This project is built for fleet management analysis and is intended for internal use.

## 🤝 Support

For issues or questions regarding the dashboard:
1. Check the console for error messages
2. Verify CSV file format and encoding
3. Ensure all dependencies are installed correctly

---

*Built with ❤️ using Streamlit and Plotly*