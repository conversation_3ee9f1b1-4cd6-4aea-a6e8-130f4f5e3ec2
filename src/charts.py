"""
Chart generation module for vehicle fleet management dashboard.
Creates interactive Plotly charts.
"""
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
from typing import Dict, Any
import streamlit as st


class ChartGenerator:
    """Generates interactive charts for the fleet management dashboard."""

    def __init__(self, theme: str = "plotly_white"):
        """
        Initialize the chart generator.

        Args:
            theme (str): Plotly theme to use
        """
        self.theme = theme
        self.colors = px.colors.qualitative.Set3

    def _ensure_datetime_column(self, df: pd.DataFrame, column: str) -> pd.DataFrame:
        """
        Ensure a column is in datetime format.

        Args:
            df (pd.DataFrame): Input dataframe
            column (str): Column name to convert

        Returns:
            pd.DataFrame: Dataframe with converted datetime column
        """
        df_copy = df.copy()
        try:
            if column in df_copy.columns and not pd.api.types.is_datetime64_any_dtype(df_copy[column]):
                df_copy[column] = pd.to_datetime(df_copy[column], errors='coerce')
        except Exception:
            pass  # If conversion fails, leave as is
        return df_copy
    
    def create_vehicles_by_category_chart(self, df: pd.DataFrame) -> go.Figure:
        """
        Create bar chart showing number of vehicles per category.

        Args:
            df (pd.DataFrame): Dataframe with vehicle data

        Returns:
            go.Figure: Plotly figure
        """
        if df.empty:
            return self._create_empty_chart("No data available")

        # Use only necessary columns for better performance
        if 'CATEGORIA' not in df.columns:
            return self._create_empty_chart("Category column not found")

        # Count vehicles by category (aggregated data)
        # Convert categorical to string to avoid Plotly issues
        categoria_series = df['CATEGORIA'].astype(str) if df['CATEGORIA'].dtype.name == 'category' else df['CATEGORIA']
        category_counts = categoria_series.value_counts().reset_index()
        category_counts.columns = ['CATEGORIA', 'Count']

        # Limit to top 15 categories for better visualization
        category_counts = category_counts.head(15)

        # Create bar chart using graph_objects to avoid Plotly express issues
        fig = go.Figure(data=[
            go.Bar(
                x=category_counts['CATEGORIA'],
                y=category_counts['Count'],
                marker_color='#667eea'
            )
        ])

        fig.update_layout(
            title='Numero di Veicoli per Categoria',
            xaxis_title='Categoria',
            yaxis_title='Conteggio'
        )
        
        fig.update_layout(
            showlegend=False,
            height=400,
            title_font_size=16,
            title_x=0.5
        )
        
        fig.update_xaxes(title_text="Category")
        fig.update_yaxes(title_text="Number of Vehicles")
        
        return fig
    
    def create_revenue_comparison_chart(self, df: pd.DataFrame) -> go.Figure:
        """
        Create stacked bar chart comparing CANONE IMPONIBILE vs CANONE IVATO by group.

        Args:
            df (pd.DataFrame): Dataframe with vehicle data

        Returns:
            go.Figure: Plotly figure
        """
        if df.empty:
            return self._create_empty_chart("No data available")

        # Check required columns
        required_cols = ['GRUPPO', 'CANONE IMPONIBILE', 'CANONE IVATO']
        if not all(col in df.columns for col in required_cols):
            return self._create_empty_chart("Required revenue columns not found")

        # Use only necessary columns and aggregate data
        revenue_data = df[required_cols].copy()

        # Convert categorical to string to avoid groupby issues
        if revenue_data['GRUPPO'].dtype.name == 'category':
            revenue_data['GRUPPO'] = revenue_data['GRUPPO'].astype(str)

        # Group by GRUPPO and sum the revenues (aggregated data)
        revenue_by_group = revenue_data.groupby('GRUPPO').agg({
            'CANONE IMPONIBILE': 'sum',
            'CANONE IVATO': 'sum'
        }).reset_index()

        # Limit to top 10 groups for better visualization
        revenue_by_group = revenue_by_group.nlargest(10, 'CANONE IVATO')
        
        fig = go.Figure()
        
        fig.add_trace(go.Bar(
            name='CANONE IMPONIBILE',
            x=revenue_by_group['GRUPPO'],
            y=revenue_by_group['CANONE IMPONIBILE'],
            marker_color=self.colors[0]
        ))
        
        fig.add_trace(go.Bar(
            name='CANONE IVATO',
            x=revenue_by_group['GRUPPO'],
            y=revenue_by_group['CANONE IVATO'],
            marker_color=self.colors[1]
        ))
        
        fig.update_layout(
            title='Revenue Comparison by Group (CANONE IMPONIBILE vs CANONE IVATO)',
            title_font_size=16,
            title_x=0.5,
            barmode='group',
            height=400,
            template=self.theme,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )
        
        fig.update_xaxes(title_text="Vehicle Group")
        fig.update_yaxes(title_text="Revenue (€)")
        
        return fig
    
    def create_lease_timeline_chart(self, df: pd.DataFrame) -> go.Figure:
        """
        Create timeline chart showing leases ending by month.

        Args:
            df (pd.DataFrame): Dataframe with vehicle data

        Returns:
            go.Figure: Plotly figure
        """
        if df.empty:
            return self._create_empty_chart("No data available")

        # Check required column
        if 'DATA FINE' not in df.columns:
            return self._create_empty_chart("End date column not found")

        # Use only necessary column for better performance
        date_data = df[['DATA FINE']].copy()

        # Ensure DATA FINE is in datetime format
        date_data = self._ensure_datetime_column(date_data, 'DATA FINE')

        # Filter out rows with missing or invalid DATA FINE
        date_data = date_data.dropna(subset=['DATA FINE'])

        if date_data.empty:
            return self._create_empty_chart("No valid lease end dates available")

        try:
            # Group by year-month (aggregated data)
            date_data['Year_Month'] = date_data['DATA FINE'].dt.to_period('M')
        except Exception as e:
            return self._create_empty_chart(f"Error processing dates: {str(e)}")
        timeline_data = date_data.groupby('Year_Month').size().reset_index(name='Count')
        timeline_data['Year_Month_Str'] = timeline_data['Year_Month'].astype(str)
        
        fig = px.line(
            timeline_data,
            x='Year_Month_Str',
            y='Count',
            title='Lease Endings by Month',
            markers=True,
            template=self.theme,
            color_discrete_sequence=[self.colors[2]]
        )
        
        fig.update_layout(
            height=400,
            title_font_size=16,
            title_x=0.5,
            showlegend=False
        )
        
        fig.update_xaxes(title_text="Month", tickangle=45)
        fig.update_yaxes(title_text="Number of Leases Ending")
        
        return fig
    
    def create_status_distribution_chart(self, df: pd.DataFrame) -> go.Figure:
        """
        Create pie chart showing vehicle status distribution.
        
        Args:
            df (pd.DataFrame): Dataframe with vehicle data
            
        Returns:
            go.Figure: Plotly figure
        """
        if df.empty:
            return self._create_empty_chart("No data available")

        # Check required column
        if 'STATO' not in df.columns:
            return self._create_empty_chart("Status column not found")

        # Convert categorical to string to avoid issues
        stato_series = df['STATO'].astype(str) if df['STATO'].dtype.name == 'category' else df['STATO']
        status_counts = stato_series.value_counts().reset_index()
        status_counts.columns = ['STATO', 'Count']
        
        fig = px.pie(
            status_counts,
            values='Count',
            names='STATO',
            title='Vehicle Status Distribution',
            color_discrete_sequence=self.colors,
            template=self.theme
        )
        
        fig.update_layout(
            height=400,
            title_font_size=16,
            title_x=0.5
        )
        
        return fig
    
    def create_location_chart(self, df: pd.DataFrame) -> go.Figure:
        """
        Create bar chart showing vehicle distribution by location.

        Args:
            df (pd.DataFrame): Dataframe with vehicle data

        Returns:
            go.Figure: Plotly figure
        """
        if df.empty:
            return self._create_empty_chart("No data available")

        # Check required column
        if 'PARCO/SEDE' not in df.columns:
            return self._create_empty_chart("Location column not found")

        # Use only necessary column for better performance
        location_data = df[['PARCO/SEDE']].copy()

        # Convert categorical to string to avoid issues
        if location_data['PARCO/SEDE'].dtype.name == 'category':
            location_data['PARCO/SEDE'] = location_data['PARCO/SEDE'].astype(str)

        # Count locations (aggregated data)
        location_counts = location_data['PARCO/SEDE'].value_counts().reset_index()
        location_counts.columns = ['PARCO/SEDE', 'Count']

        # Take top 10 locations for better visualization
        location_counts = location_counts.head(10)
        
        fig = px.bar(
            location_counts,
            x='Count',
            y='PARCO/SEDE',
            title='Top 10 Locations by Vehicle Count',
            orientation='h',
            color='Count',
            color_continuous_scale='Blues',
            template=self.theme
        )
        
        fig.update_layout(
            height=400,
            title_font_size=16,
            title_x=0.5,
            showlegend=False
        )
        
        fig.update_xaxes(title_text="Number of Vehicles")
        fig.update_yaxes(title_text="Location")
        
        return fig
    
    def _create_empty_chart(self, message: str) -> go.Figure:
        """
        Create empty chart with message.
        
        Args:
            message (str): Message to display
            
        Returns:
            go.Figure: Empty plotly figure
        """
        fig = go.Figure()
        fig.add_annotation(
            text=message,
            x=0.5,
            y=0.5,
            xref="paper",
            yref="paper",
            showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(
            height=400,
            template=self.theme,
            xaxis=dict(showgrid=False, showticklabels=False, zeroline=False),
            yaxis=dict(showgrid=False, showticklabels=False, zeroline=False)
        )
        return fig

    def create_expiration_cost_timeline_chart(self, timeline_data: pd.DataFrame) -> go.Figure:
        """
        Create timeline chart showing monthly cost reductions from lease expirations.

        Args:
            timeline_data (pd.DataFrame): Timeline data with expiration information

        Returns:
            go.Figure: Plotly figure
        """
        if timeline_data.empty:
            return self._create_empty_chart("No future lease expirations available")

        # Create dual-axis chart
        fig = go.Figure()

        # Add monthly cost reduction bars
        fig.add_trace(go.Bar(
            x=timeline_data['Month_Year'],
            y=timeline_data['Monthly_Cost_Reduction'],
            name='Monthly Cost Reduction',
            marker_color='#ff6b6b',
            text=[f"€{val:,.0f}" for val in timeline_data['Monthly_Cost_Reduction']],
            textposition='outside'
        ))

        # Add remaining monthly cost line
        fig.add_trace(go.Scatter(
            x=timeline_data['Month_Year'],
            y=timeline_data['Remaining_Monthly_Cost'],
            mode='lines+markers',
            name='Remaining Monthly Cost',
            line=dict(color='#667eea', width=3),
            text=[f"€{val:,.0f}" for val in timeline_data['Remaining_Monthly_Cost']],
            textposition='top center'
        ))

        # Aggiorna layout
        fig.update_layout(
            title='Timeline Riduzione Costi Mensili Flotta',
            xaxis_title='Mese/Anno',
            yaxis_title='Costo Mensile (€)',
            legend=dict(x=0.7, y=1),
            hovermode='x unified',
            height=500
        )

        # Rotate x-axis labels for better readability
        fig.update_xaxes(tickangle=45)

        return fig