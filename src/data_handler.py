"""
Data handler module for vehicle fleet management dashboard.
Handles CSV loading, filtering, and data transformations.
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import streamlit as st


class FleetDataHandler:
    """Handles all data operations for the fleet management dashboard."""
    
    def __init__(self, csv_path: str):
        """
        Initialize the data handler with CSV file path.
        
        Args:
            csv_path (str): Path to the CSV file
        """
        self.csv_path = csv_path
        self.df = None
        self._load_data()
    
    def _load_data(self) -> None:
        """Load and preprocess the CSV data."""
        try:
            # Load CSV with UTF-8-SIG encoding
            self.df = pd.read_csv(self.csv_path, encoding='utf-8-sig')
            
            # Clean and preprocess data
            self._preprocess_data()
            
        except Exception as e:
            st.error(f"Error loading data: {str(e)}")
            self.df = pd.DataFrame()
    
    def _preprocess_data(self) -> None:
        """Preprocess the loaded data with memory optimizations."""
        if self.df is None or self.df.empty:
            return

        print(f"📊 Original data: {len(self.df):,} rows, {self.df.memory_usage(deep=True).sum() / 1024 / 1024:.1f} MB")

        # CRITICAL: Remove duplicates first to reduce memory usage
        initial_rows = len(self.df)
        self.df = self.df.drop_duplicates()
        duplicates_removed = initial_rows - len(self.df)
        if duplicates_removed > 0:
            print(f"🗑️  Removed {duplicates_removed:,} duplicate rows ({duplicates_removed/initial_rows*100:.1f}%)")

        # Optimize data types for categorical columns
        categorical_columns = ['CATEGORIA', 'GRUPPO', 'STATO', 'TIPOLOGIA', 'TRASMISSIONE',
                              'PROPRIETÀ', 'ACQUISIZIONE', 'PARCO/SEDE']
        for col in categorical_columns:
            if col in self.df.columns:
                self.df[col] = self.df[col].astype('category')

        # Convert date columns to datetime
        date_columns = ['DATA INIZIO', 'DATA FINE', 'DATA EFFETTIVA', 'RICONSEGNA']
        for col in date_columns:
            if col in self.df.columns:
                self.df[col] = pd.to_datetime(self.df[col], format='%m/%d/%y', errors='coerce')

        # Clean monetary columns
        money_columns = ['CANONE IMPONIBILE', 'CANONE IVATO', 'COPERTURA ASS.']
        for col in money_columns:
            if col in self.df.columns:
                # Remove currency symbols and convert to float
                self.df[col] = self.df[col].astype(str).str.replace('€', '').str.replace(',', '').str.strip()
                self.df[col] = pd.to_numeric(self.df[col], errors='coerce')

        # Clean numeric columns and optimize data types
        if 'KM INCLUSI' in self.df.columns:
            self.df['KM INCLUSI'] = self.df['KM INCLUSI'].astype(str).str.replace(',', '').str.strip()
            self.df['KM INCLUSI'] = pd.to_numeric(self.df['KM INCLUSI'], errors='coerce')
            # Use int32 if values fit
            if self.df['KM INCLUSI'].max() < 2147483647:
                self.df['KM INCLUSI'] = self.df['KM INCLUSI'].astype('Int32')

        if 'DURATA IN MESI' in self.df.columns:
            self.df['DURATA IN MESI'] = pd.to_numeric(self.df['DURATA IN MESI'], errors='coerce')
            # Use int8 for duration (max 255 months)
            if self.df['DURATA IN MESI'].max() <= 255:
                self.df['DURATA IN MESI'] = self.df['DURATA IN MESI'].astype('Int8')

        # Optimize remaining object columns
        for col in self.df.columns:
            if self.df[col].dtype == 'object' and col not in categorical_columns:
                # Convert to string type for better memory efficiency
                self.df[col] = self.df[col].astype('string')

        print(f"✅ Optimized data: {len(self.df):,} rows, {self.df.memory_usage(deep=True).sum() / 1024 / 1024:.1f} MB")
    
    def get_data(self) -> pd.DataFrame:
        """Return the current dataframe."""
        return self.df.copy() if self.df is not None else pd.DataFrame()
    
    def apply_filters(self, filters: Dict) -> pd.DataFrame:
        """
        Apply filters to the dataframe.
        
        Args:
            filters (Dict): Dictionary of filter criteria
            
        Returns:
            pd.DataFrame: Filtered dataframe
        """
        if self.df is None or self.df.empty:
            return pd.DataFrame()
        
        filtered_df = self.df.copy()
        
        # Category filters
        if filters.get('categoria') and len(filters['categoria']) > 0:
            filtered_df = filtered_df[filtered_df['CATEGORIA'].isin(filters['categoria'])]
        
        if filters.get('gruppo') and len(filters['gruppo']) > 0:
            filtered_df = filtered_df[filtered_df['GRUPPO'].isin(filters['gruppo'])]
        
        if filters.get('stato') and len(filters['stato']) > 0:
            filtered_df = filtered_df[filtered_df['STATO'].isin(filters['stato'])]

        # Location filter
        if filters.get('location') and len(filters['location']) > 0:
            filtered_df = filtered_df[filtered_df['PARCO/SEDE'].isin(filters['location'])]

        # Company filter (using PROPRIETÀ column)
        if filters.get('company') and len(filters['company']) > 0:
            filtered_df = filtered_df[filtered_df['PROPRIETÀ'].isin(filters['company'])]

        # Insurance status filter
        if filters.get('insurance_status') and len(filters['insurance_status']) > 0:
            if 'COPERTURA ASS.' in filtered_df.columns:
                insurance_conditions = []
                for status in filters['insurance_status']:
                    if status in ['Insured', 'Assicurato']:
                        insurance_conditions.append(
                            (filtered_df['COPERTURA ASS.'] > 0) &
                            (filtered_df['COPERTURA ASS.'].notna())
                        )
                    elif status in ['Uninsured', 'Non Assicurato']:
                        insurance_conditions.append(
                            (filtered_df['COPERTURA ASS.'] == 0) |
                            (filtered_df['COPERTURA ASS.'].isna())
                        )

                if insurance_conditions:
                    # Combine conditions with OR
                    combined_condition = insurance_conditions[0]
                    for condition in insurance_conditions[1:]:
                        combined_condition = combined_condition | condition
                    filtered_df = filtered_df[combined_condition]

        # Enhanced date range filtering
        if filters.get('date_range'):
            start_date, end_date = filters['date_range']
            date_field = filters.get('date_field', 'DATA FINE')  # Default to end date

            if date_field in filtered_df.columns:
                if start_date:
                    filtered_df = filtered_df[filtered_df[date_field] >= start_date]
                if end_date:
                    filtered_df = filtered_df[filtered_df[date_field] <= end_date]

        # Legacy date filters (for backward compatibility)
        if filters.get('data_inizio_range'):
            start_date, end_date = filters['data_inizio_range']
            if start_date:
                filtered_df = filtered_df[filtered_df['DATA INIZIO'] >= start_date]
            if end_date:
                filtered_df = filtered_df[filtered_df['DATA INIZIO'] <= end_date]

        if filters.get('data_fine_range'):
            start_date, end_date = filters['data_fine_range']
            if start_date:
                filtered_df = filtered_df[filtered_df['DATA FINE'] >= start_date]
            if end_date:
                filtered_df = filtered_df[filtered_df['DATA FINE'] <= end_date]
        
        return filtered_df
    
    def calculate_kpis(self, filtered_df: pd.DataFrame) -> Dict:
        """
        Calculate KPIs for the fleet leasing cost dashboard.

        Args:
            filtered_df (pd.DataFrame): Filtered dataframe

        Returns:
            Dict: Dictionary of KPI values representing monthly lease costs and fleet status
        """
        if filtered_df.empty:
            return {
                'total_vehicles': 0,
                'monthly_fleet_costs': 0,
                'expiring_soon': 0,
                'active_vehicles': 0
            }

        today = datetime.now()

        # Total active vehicles
        active_vehicles = len(filtered_df[filtered_df['STATO'] == 'Attivo'])

        # Monthly fleet costs (CANONE IMPONIBILE represents monthly lease costs)
        active_fleet = filtered_df[filtered_df['STATO'] == 'Attivo']
        monthly_fleet_costs = active_fleet['CANONE IMPONIBILE'].sum()

        # Vehicles expiring within 90 days
        expiring_threshold = today + timedelta(days=90)
        expiring_soon = 0
        if 'DATA FINE' in filtered_df.columns:
            expiring_soon = len(filtered_df[
                (filtered_df['DATA FINE'] <= expiring_threshold) &
                (filtered_df['DATA FINE'] >= today) &
                (filtered_df['STATO'] == 'Attivo')
            ])

        # Calculate VAT amounts
        active_fleet = filtered_df[filtered_df['STATO'] == 'Attivo']
        monthly_fleet_costs_with_vat = active_fleet['CANONE IVATO'].sum()

        # Calculate insurance status
        insured_vehicles = 0
        uninsured_vehicles = 0
        if 'COPERTURA ASS.' in filtered_df.columns:
            insured_vehicles = len(filtered_df[
                (filtered_df['COPERTURA ASS.'] > 0) &
                (filtered_df['COPERTURA ASS.'].notna())
            ])
            uninsured_vehicles = len(filtered_df) - insured_vehicles

        return {
            'total_vehicles': len(filtered_df),
            'monthly_fleet_costs': monthly_fleet_costs,
            'monthly_fleet_costs_with_vat': monthly_fleet_costs_with_vat,
            'expiring_soon': expiring_soon,
            'active_vehicles': active_vehicles,
            'insured_vehicles': insured_vehicles,
            'uninsured_vehicles': uninsured_vehicles
        }

    def calculate_expiration_impact_kpis(self, filtered_df: pd.DataFrame) -> Dict:
        """
        Calculate lease expiration impact KPIs.

        Args:
            filtered_df (pd.DataFrame): Filtered dataframe

        Returns:
            Dict: Dictionary of expiration impact KPI values
        """
        if filtered_df.empty or 'DATA FINE' not in filtered_df.columns:
            return {
                'next_month_cost_reduction': 0,
                'q1_cost_impact': 0,
                'annual_savings_from_expirations': 0
            }

        today = datetime.now()
        active_fleet = filtered_df[filtered_df['STATO'] == 'Attivo']

        # Next month cost reduction (next 30 days)
        next_month_threshold = today + timedelta(days=30)
        next_month_expiring = active_fleet[
            (active_fleet['DATA FINE'] >= today) &
            (active_fleet['DATA FINE'] <= next_month_threshold)
        ]
        next_month_cost_reduction = next_month_expiring['CANONE IMPONIBILE'].sum()

        # Q1 cost impact (next 3 months)
        q1_threshold = today + timedelta(days=90)
        q1_expiring = active_fleet[
            (active_fleet['DATA FINE'] >= today) &
            (active_fleet['DATA FINE'] <= q1_threshold)
        ]
        q1_cost_impact = q1_expiring['CANONE IMPONIBILE'].sum()

        # Annual savings from all future expirations
        future_expiring = active_fleet[active_fleet['DATA FINE'] >= today]
        annual_savings_from_expirations = future_expiring['CANONE IMPONIBILE'].sum()

        # Calculate VAT amounts for expiration impact
        next_month_cost_reduction_with_vat = next_month_expiring['CANONE IVATO'].sum()
        q1_cost_impact_with_vat = q1_expiring['CANONE IVATO'].sum()
        annual_savings_from_expirations_with_vat = future_expiring['CANONE IVATO'].sum()

        return {
            'next_month_cost_reduction': next_month_cost_reduction,
            'next_month_cost_reduction_with_vat': next_month_cost_reduction_with_vat,
            'q1_cost_impact': q1_cost_impact,
            'q1_cost_impact_with_vat': q1_cost_impact_with_vat,
            'annual_savings_from_expirations': annual_savings_from_expirations,
            'annual_savings_from_expirations_with_vat': annual_savings_from_expirations_with_vat
        }

    def get_expiration_timeline_data(self, filtered_df: pd.DataFrame) -> pd.DataFrame:
        """
        Generate expiration timeline data for cost reduction analysis.

        Args:
            filtered_df (pd.DataFrame): Filtered dataframe

        Returns:
            pd.DataFrame: Timeline data with monthly cost reductions
        """
        if filtered_df.empty or 'DATA FINE' not in filtered_df.columns:
            return pd.DataFrame()

        today = datetime.now()
        active_fleet = filtered_df[filtered_df['STATO'] == 'Attivo']

        # Filter for future expirations only
        future_expirations = active_fleet[active_fleet['DATA FINE'] >= today].copy()

        if future_expirations.empty:
            return pd.DataFrame()

        # Group by year-month
        future_expirations['Year_Month'] = future_expirations['DATA FINE'].dt.to_period('M')

        # Calculate monthly statistics including VAT
        monthly_stats = future_expirations.groupby('Year_Month').agg({
            'CANONE IMPONIBILE': ['count', 'sum'],
            'CANONE IVATO': 'sum',
            'TARGA': 'count'
        }).reset_index()

        # Flatten column names
        monthly_stats.columns = ['Year_Month', 'Vehicle_Count', 'Monthly_Cost_Reduction', 'Monthly_Cost_Reduction_VAT', 'Vehicle_Count_Check']
        monthly_stats = monthly_stats.drop('Vehicle_Count_Check', axis=1)

        # Sort by date
        monthly_stats = monthly_stats.sort_values('Year_Month')

        # Calculate cumulative savings and remaining costs (both pre-tax and with VAT)
        current_monthly_cost = active_fleet['CANONE IMPONIBILE'].sum()
        current_monthly_cost_vat = active_fleet['CANONE IVATO'].sum()

        monthly_stats['Cumulative_Savings'] = monthly_stats['Monthly_Cost_Reduction'].cumsum()
        monthly_stats['Cumulative_Savings_VAT'] = monthly_stats['Monthly_Cost_Reduction_VAT'].cumsum()
        monthly_stats['Remaining_Monthly_Cost'] = current_monthly_cost - monthly_stats['Cumulative_Savings']
        monthly_stats['Remaining_Monthly_Cost_VAT'] = current_monthly_cost_vat - monthly_stats['Cumulative_Savings_VAT']
        monthly_stats['Percentage_Reduction'] = (monthly_stats['Monthly_Cost_Reduction'] / current_monthly_cost * 100).round(2)

        # Convert Year_Month back to string for display
        monthly_stats['Month_Year'] = monthly_stats['Year_Month'].astype(str)

        return monthly_stats

    def calculate_cost_savings_from_early_returns(self, filtered_df: pd.DataFrame) -> Dict:
        """
        Calculate cost savings from early vehicle returns (when delivery date < end date).

        Args:
            filtered_df (pd.DataFrame): Filtered dataframe

        Returns:
            Dict: Dictionary containing cost savings analysis
        """
        if filtered_df.empty or 'DATA EFFETTIVA' not in filtered_df.columns or 'DATA FINE' not in filtered_df.columns:
            return {
                'total_savings': 0,
                'total_savings_with_vat': 0,
                'total_days_saved': 0,
                'vehicles_with_early_returns': 0,
                'early_return_details': pd.DataFrame()
            }

        # Filter for active vehicles with both delivery and end dates
        active_fleet = filtered_df[
            (filtered_df['STATO'] == 'Attivo') &
            (filtered_df['DATA EFFETTIVA'].notna()) &
            (filtered_df['DATA FINE'].notna())
        ].copy()

        if active_fleet.empty:
            return {
                'total_savings': 0,
                'total_savings_with_vat': 0,
                'total_days_saved': 0,
                'vehicles_with_early_returns': 0,
                'early_return_details': pd.DataFrame()
            }

        # Identify early returns (delivery date < end date)
        early_returns = active_fleet[active_fleet['DATA EFFETTIVA'] < active_fleet['DATA FINE']].copy()

        if early_returns.empty:
            return {
                'total_savings': 0,
                'total_savings_with_vat': 0,
                'total_days_saved': 0,
                'vehicles_with_early_returns': 0,
                'early_return_details': pd.DataFrame()
            }

        # Calculate savings for each vehicle
        early_returns['days_saved'] = (early_returns['DATA FINE'] - early_returns['DATA EFFETTIVA']).dt.days

        # Calculate daily lease cost (monthly cost / average days in month)
        avg_days_per_month = 30.44  # More accurate than 30
        early_returns['daily_cost'] = early_returns['CANONE IMPONIBILE'] / avg_days_per_month
        early_returns['daily_cost_with_vat'] = early_returns['CANONE IVATO'] / avg_days_per_month

        # Calculate total savings per vehicle
        early_returns['savings_per_vehicle'] = early_returns['days_saved'] * early_returns['daily_cost']
        early_returns['savings_per_vehicle_with_vat'] = early_returns['days_saved'] * early_returns['daily_cost_with_vat']

        # Calculate totals
        total_savings = early_returns['savings_per_vehicle'].sum()
        total_savings_with_vat = early_returns['savings_per_vehicle_with_vat'].sum()
        total_days_saved = early_returns['days_saved'].sum()
        vehicles_with_early_returns = len(early_returns)

        # Prepare details for display
        early_return_details = early_returns[[
            'TARGA', 'MODELLO', 'DATA FINE', 'DATA EFFETTIVA', 'days_saved',
            'CANONE IMPONIBILE', 'daily_cost', 'savings_per_vehicle'
        ]].copy()

        return {
            'total_savings': total_savings,
            'total_savings_with_vat': total_savings_with_vat,
            'total_days_saved': total_days_saved,
            'vehicles_with_early_returns': vehicles_with_early_returns,
            'early_return_details': early_return_details,
            'avg_days_saved_per_vehicle': total_days_saved / vehicles_with_early_returns if vehicles_with_early_returns > 0 else 0,
            'avg_savings_per_vehicle': total_savings / vehicles_with_early_returns if vehicles_with_early_returns > 0 else 0
        }

    def get_unique_values(self, column: str) -> List[str]:
        """
        Get unique values from a column for filter options.
        
        Args:
            column (str): Column name
            
        Returns:
            List[str]: List of unique values
        """
        if self.df is None or column not in self.df.columns:
            return []
        
        return sorted(self.df[column].dropna().unique().tolist())
    
    def save_data(self, updated_df: pd.DataFrame) -> bool:
        """
        Save updated dataframe back to CSV.
        
        Args:
            updated_df (pd.DataFrame): Updated dataframe
            
        Returns:
            bool: Success status
        """
        try:
            # Convert datetime columns back to string format
            df_to_save = updated_df.copy()

            date_columns = ['DATA INIZIO', 'DATA FINE', 'DATA EFFETTIVA', 'RICONSEGNA']
            for col in date_columns:
                if col in df_to_save.columns:
                    try:
                        # Check if column is already datetime, if not try to convert it
                        if not pd.api.types.is_datetime64_any_dtype(df_to_save[col]):
                            # Try multiple date formats that AgGrid might produce
                            df_to_save[col] = pd.to_datetime(df_to_save[col], errors='coerce')

                        # Only apply dt.strftime if the column is datetime type
                        if pd.api.types.is_datetime64_any_dtype(df_to_save[col]):
                            df_to_save[col] = df_to_save[col].dt.strftime('%m/%d/%y')
                        # If it's not datetime and conversion failed, leave as is (likely already string)
                    except Exception as e:
                        # If any error occurs, just leave the column as is
                        print(f"Warning: Could not process date column {col}: {str(e)}")
                        pass
            
            # Format monetary columns
            money_columns = ['CANONE IMPONIBILE', 'CANONE IVATO', 'COPERTURA ASS.']
            for col in money_columns:
                if col in df_to_save.columns:
                    df_to_save[col] = df_to_save[col].apply(lambda x: f"{x:.2f} €" if pd.notna(x) else "")
            
            # Format KM INCLUSI with commas
            if 'KM INCLUSI' in df_to_save.columns:
                df_to_save['KM INCLUSI'] = df_to_save['KM INCLUSI'].apply(
                    lambda x: f"{int(x):,}" if pd.notna(x) else ""
                )
            
            # Save to CSV
            df_to_save.to_csv(self.csv_path, index=False, encoding='utf-8-sig')
            self.df = updated_df  # Update internal dataframe
            
            return True
            
        except Exception as e:
            st.error(f"Error saving data: {str(e)}")
            return False
    
    def export_filtered_data(self, filtered_df: pd.DataFrame, filename: str) -> bytes:
        """
        Export filtered data to CSV format.

        Args:
            filtered_df (pd.DataFrame): Filtered dataframe
            filename (str): Output filename

        Returns:
            bytes: CSV data as bytes
        """
        df_to_export = filtered_df.copy()

        # Format for export similar to save_data
        date_columns = ['DATA INIZIO', 'DATA FINE', 'DATA EFFETTIVA', 'RICONSEGNA']
        for col in date_columns:
            if col in df_to_export.columns:
                try:
                    # Check if column is already datetime, if not try to convert it
                    if not pd.api.types.is_datetime64_any_dtype(df_to_export[col]):
                        # Try multiple date formats that AgGrid might produce
                        df_to_export[col] = pd.to_datetime(df_to_export[col], errors='coerce')

                    # Only apply dt.strftime if the column is datetime type
                    if pd.api.types.is_datetime64_any_dtype(df_to_export[col]):
                        df_to_export[col] = df_to_export[col].dt.strftime('%m/%d/%y')
                    # If it's not datetime and conversion failed, leave as is (likely already string)
                except Exception as e:
                    # If any error occurs, just leave the column as is
                    print(f"Warning: Could not process date column {col}: {str(e)}")
                    pass
        
        money_columns = ['CANONE IMPONIBILE', 'CANONE IVATO', 'COPERTURA ASS.']
        for col in money_columns:
            if col in df_to_export.columns:
                df_to_export[col] = df_to_export[col].apply(lambda x: f"{x:.2f} €" if pd.notna(x) else "")
        
        if 'KM INCLUSI' in df_to_export.columns:
            df_to_export['KM INCLUSI'] = df_to_export['KM INCLUSI'].apply(
                lambda x: f"{int(x):,}" if pd.notna(x) else ""
            )
        
        return df_to_export.to_csv(index=False, encoding='utf-8-sig').encode('utf-8-sig')