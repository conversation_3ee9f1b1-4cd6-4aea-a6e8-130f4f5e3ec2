"""
Dashboard Costi Noleggio Flotta - Applicazione Streamlit Principale
Una dashboard moderna e reattiva per l'analisi e la gestione dei costi di noleggio della flotta veicoli.
"""
import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import plotly.express as px
from st_aggrid import AgGrid, GridOptionsBuilder, GridUpdateMode, DataReturnMode
from st_aggrid.shared import JsCode
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data_handler import FleetDataHandler
from charts import ChartGenerator

# Configurazione pagina
st.set_page_config(
    page_title="Dashboard Costi Noleggio Flotta",
    page_icon="🚗",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced CSS for modern UI with navigation
st.markdown("""
<style>
    /* Hide default Streamlit elements */
    .stDeployButton {display: none;}
    #MainMenu {visibility: hidden;}
    .stAppHeader {display: none;}

    /* Main layout */
    .main-container {
        display: flex;
        min-height: 100vh;
        background-color: #fafafa;
    }

    /* Sidebar Navigation */
    .nav-sidebar {
        background: white;
        border-right: 1px solid #e5e7eb;
        transition: width 0.3s ease;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        position: fixed;
        height: 100vh;
        z-index: 1000;
    }

    .nav-sidebar.expanded {
        width: 280px;
    }

    .nav-sidebar.collapsed {
        width: 80px;
    }

    .nav-header {
        padding: 1.5rem 1rem;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .nav-logo {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .nav-logo-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
    }

    .nav-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
    }

    .nav-subtitle {
        font-size: 0.75rem;
        color: #6b7280;
    }

    .nav-toggle {
        background: none;
        border: none;
        padding: 0.5rem;
        border-radius: 6px;
        cursor: pointer;
        color: #6b7280;
        transition: all 0.2s;
    }

    .nav-toggle:hover {
        background-color: #f3f4f6;
        color: #374151;
    }

    .nav-menu {
        padding: 1rem 0;
    }

    .nav-section {
        margin-bottom: 1.5rem;
    }

    .nav-section-label {
        padding: 0 1rem 0.5rem 1rem;
        font-size: 0.75rem;
        font-weight: 600;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .nav-item {
        margin: 0.25rem 0.5rem;
        border-radius: 8px;
        transition: all 0.2s;
        cursor: pointer;
    }

    .nav-item:hover {
        background-color: #f3f4f6;
    }

    .nav-item.active {
        background-color: #667eea;
        color: white;
    }

    .nav-item.active .nav-item-icon,
    .nav-item.active .nav-item-text,
    .nav-item.active .nav-item-desc {
        color: white;
    }

    .nav-item-content {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        gap: 0.75rem;
    }

    .nav-item-icon {
        width: 20px;
        height: 20px;
        color: #6b7280;
        flex-shrink: 0;
    }

    .nav-item-text {
        font-weight: 500;
        color: #374151;
        font-size: 0.875rem;
    }

    .nav-item-desc {
        font-size: 0.75rem;
        color: #6b7280;
        margin-top: 0.125rem;
    }

    /* Main content area */
    .main-content {
        flex: 1;
        transition: margin-left 0.3s ease;
        min-height: 100vh;
    }

    .main-content.expanded {
        margin-left: 280px;
    }

    .main-content.collapsed {
        margin-left: 80px;
    }

    .content-header {
        background: white;
        border-bottom: 1px solid #e5e7eb;
        padding: 1rem 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .breadcrumb-item {
        color: #6b7280;
    }

    .breadcrumb-item.active {
        color: #374151;
        font-weight: 500;
    }

    .breadcrumb-separator {
        color: #d1d5db;
    }

    .filter-toggle {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 6px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-toggle:hover {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        transform: translateY(-1px);
    }

    .content-body {
        padding: 2rem;
    }

    /* Enhanced KPI Cards */
    .kpi-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        transition: all 0.2s;
        margin-bottom: 1rem;
    }

    .kpi-card:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
    }

    .kpi-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

    .kpi-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .kpi-icon {
        width: 16px;
        height: 16px;
        color: #6b7280;
    }

    .kpi-value {
        font-size: 2rem;
        font-weight: 700;
        color: #111827;
        margin-bottom: 0.5rem;
    }

    .kpi-footer {
        display: flex;
        align-items: center;
        justify-content: between;
    }

    .kpi-subtitle {
        font-size: 0.75rem;
        color: #6b7280;
        flex: 1;
    }

    .kpi-trend {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
    }

    .kpi-trend.positive {
        color: #059669;
        background-color: #d1fae5;
    }

    .kpi-trend.negative {
        color: #dc2626;
        background-color: #fee2e2;
    }

    .kpi-trend.neutral {
        color: #6b7280;
        background-color: #f3f4f6;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .nav-sidebar.expanded {
            width: 100%;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        .main-content.collapsed {
            margin-left: 0;
        }
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'data_handler' not in st.session_state:
    st.session_state.data_handler = None
    st.session_state.current_filters = {}
    st.session_state.filtered_data = pd.DataFrame()

# Always create a fresh chart generator to avoid caching issues
chart_generator = ChartGenerator()

@st.cache_data
def load_data():
    """Load and cache the fleet data."""
    try:
        data_handler = FleetDataHandler("RegistroFLOTTA_Master.csv")
        return data_handler
    except Exception as e:
        st.error(f"Error loading data: {str(e)}")
        return None

# Navigation and UI Components
def render_navigation_sidebar():
    """Render the enhanced navigation sidebar."""
    # Initialize session state for navigation
    if 'nav_expanded' not in st.session_state:
        st.session_state.nav_expanded = True
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'overview'

    # Navigation items configuration
    nav_items = {
        'overview': {
            'icon': '📊',
            'title': 'Dashboard',
            'description': 'Overview & KPIs',
            'section': 'main'
        },
        'fleet_analysis': {
            'icon': '🚗',
            'title': 'Fleet Analysis',
            'description': 'Vehicle data & charts',
            'section': 'main'
        },
        'cost_management': {
            'icon': '💰',
            'title': 'Cost Management',
            'description': 'Savings & expenses',
            'section': 'main'
        },
        'early_returns': {
            'icon': '⚡',
            'title': 'Early Returns',
            'description': 'Delivery savings analysis',
            'section': 'main'
        },
        'data_table': {
            'icon': '📋',
            'title': 'Data Table',
            'description': 'Raw data & exports',
            'section': 'data'
        },
        'settings': {
            'icon': '⚙️',
            'title': 'Settings',
            'description': 'Filters & preferences',
            'section': 'data'
        }
    }

    return nav_items

def render_content_header(current_page: str, nav_items: dict):
    """Render the content header with breadcrumbs and filter toggle."""
    current_item = nav_items.get(current_page, nav_items['overview'])

    # Generate breadcrumb
    breadcrumb_items = ['Fleet Management']
    if current_item['section'] == 'data':
        breadcrumb_items.append('Data & Tools')
    breadcrumb_items.append(current_item['title'])

    content_class = "expanded" if st.session_state.get('nav_expanded', True) else "collapsed"

    header_html = f"""
    <div class="main-content {content_class}">
        <div class="content-header">
            <div class="breadcrumb">
    """

    for i, item in enumerate(breadcrumb_items):
        if i > 0:
            header_html += '<span class="breadcrumb-separator">›</span>'

        active_class = "active" if i == len(breadcrumb_items) - 1 else ""
        header_html += f'<span class="breadcrumb-item {active_class}">{item}</span>'

    header_html += """
            </div>
            <button class="filter-toggle" onclick="toggleFilters()">
                <span>🔍</span>
                <span>Filters</span>
            </button>
        </div>
        <div class="content-body">
    """

    st.markdown(header_html, unsafe_allow_html=True)

def create_enhanced_kpi_card(title: str, value: str, subtitle: str = "", trend: dict = None, icon: str = "📊"):
    """Create an enhanced KPI card with modern design."""
    trend_html = ""
    if trend:
        trend_class = trend.get('type', 'neutral')  # positive, negative, neutral
        trend_icon = "↗" if trend_class == 'positive' else "↘" if trend_class == 'negative' else "→"
        trend_value = trend.get('value', '')
        trend_html = f"""
            <div class="kpi-trend {trend_class}">
                <span>{trend_icon}</span>
                <span>{trend_value}</span>
            </div>
        """

    card_html = f"""
    <div class="kpi-card">
        <div class="kpi-header">
            <div class="kpi-title">
                <span class="kpi-icon">{icon}</span>
                <span>{title}</span>
            </div>
        </div>
        <div class="kpi-value">{value}</div>
        <div class="kpi-footer">
            <div class="kpi-subtitle">{subtitle}</div>
            {trend_html}
        </div>
    </div>
    """

    st.markdown(card_html, unsafe_allow_html=True)

def create_kpi_card(title: str, value: str, is_warning: bool = False, is_success: bool = False, is_info: bool = False):
    """Legacy KPI card function - redirects to enhanced version."""
    icon = "⚠️" if is_warning else "✅" if is_success else "ℹ️" if is_info else "📊"
    create_enhanced_kpi_card(title, value, icon=icon)

def render_sidebar_filters(data_handler: FleetDataHandler):
    """Renderizza i filtri della barra laterale."""
    st.sidebar.markdown("## 🔍 Filtri Globali")

    filters = {}

    # Filtro categoria
    categories = data_handler.get_unique_values('CATEGORIA')
    selected_categories = st.sidebar.multiselect(
        "Categorie",
        categories,
        key="category_filter"
    )
    filters['categoria'] = selected_categories

    # Filtro gruppo
    groups = data_handler.get_unique_values('GRUPPO')
    selected_groups = st.sidebar.multiselect(
        "Gruppi",
        groups,
        key="group_filter"
    )
    filters['gruppo'] = selected_groups
    
    # Filtro stato
    statuses = data_handler.get_unique_values('STATO')
    selected_statuses = st.sidebar.multiselect(
        "Stato",
        statuses,
        key="status_filter"
    )
    filters['stato'] = selected_statuses

    # Filtro sede
    locations = data_handler.get_unique_values('PARCO/SEDE')
    selected_locations = st.sidebar.multiselect(
        "Sedi",
        locations,
        key="location_filter"
    )
    filters['location'] = selected_locations

    # Filtro azienda (usando colonna PROPRIETÀ)
    companies = data_handler.get_unique_values('PROPRIETÀ')
    selected_companies = st.sidebar.multiselect(
        "Aziende/Proprietari",
        companies,
        key="company_filter"
    )
    filters['company'] = selected_companies

    # Filtro stato assicurazione
    insurance_options = ['Assicurato', 'Non Assicurato']
    selected_insurance_status = st.sidebar.multiselect(
        "Stato Assicurazione",
        insurance_options,
        key="insurance_filter"
    )
    filters['insurance_status'] = selected_insurance_status

    # Enhanced Date Range Filtering
    st.sidebar.markdown("### 📅 Filtro Intervallo Date")

    # Date field selection
    date_field_options = {
        'DATA FINE': 'Data Fine (data di fine contratto)',
        'DATA EFFETTIVA': 'Data Consegna (data di consegna effettiva)'
    }

    selected_date_field = st.sidebar.radio(
        "Campo Data per Filtro:",
        options=list(date_field_options.keys()),
        format_func=lambda x: date_field_options[x],
        key="date_field_selector",
        help="Scegli quale campo data utilizzare per il filtro dell'intervallo"
    )

    # Show additional info for delivery date filtering
    if selected_date_field == 'DATA EFFETTIVA':
        st.sidebar.info("💡 **Filtro Intelligente**: Quando filtri per Data Consegna, vengono inclusi anche i veicoli con consegne anticipate per preservare l'analisi dei risparmi.")
    filters['date_field'] = selected_date_field

    st.sidebar.markdown(f"**Filtro per: {date_field_options[selected_date_field]}**")

    # Checkbox to enable/disable date filtering
    enable_date_filter = st.sidebar.checkbox(
        "Abilita Filtro Date",
        value=False,
        key="enable_date_filter",
        help="Attiva per filtrare i dati per intervallo di date"
    )

    if enable_date_filter:
        # Date range selector with suggested default values
        today = datetime.now().date()
        default_start = today - timedelta(days=30)

        # Use date_input with range capability
        date_range = st.sidebar.date_input(
            "Seleziona Intervallo Date:",
            value=(default_start, today),
            key="enhanced_date_range",
            help=f"Filtra i dati basandosi sul campo '{date_field_options[selected_date_field]}'"
        )
    else:
        date_range = None

    # Handle date range input with validation only if date filter is enabled
    if enable_date_filter and date_range:
        if isinstance(date_range, tuple) and len(date_range) == 2:
            start_date, end_date = date_range
            # Validate date range
            if start_date and end_date and start_date > end_date:
                st.sidebar.error("⚠️ La data di inizio non può essere successiva alla data di fine!")
                filters['date_range'] = (end_date, start_date)  # Swap dates
            else:
                filters['date_range'] = date_range
        elif isinstance(date_range, tuple) and len(date_range) == 1:
            # Single date selected, use as start date with no end date
            filters['date_range'] = (date_range[0], None)
        else:
            # Single date object
            filters['date_range'] = (date_range, None)

        # Show current filter status
        if filters.get('date_range'):
            start, end = filters['date_range']
            if start and end:
                days_diff = (end - start).days
                st.sidebar.info(f"📅 Intervallo selezionato: {days_diff + 1} giorni")
            elif start:
                st.sidebar.info(f"📅 Filtro da: {start.strftime('%d/%m/%Y')}")
    else:
        # Date filter is disabled, don't apply date range filtering
        filters['date_range'] = None
        if not enable_date_filter:
            st.sidebar.info("📅 Filtro date disabilitato - mostrando tutti i dati")

    # Legacy date filters (for backward compatibility)
    with st.sidebar.expander("🔧 Filtri Date Avanzati (Opzionale)", expanded=False):
        st.markdown("*Filtri aggiuntivi per compatibilità con versioni precedenti*")

        # Intervallo data inizio
        col1, col2 = st.sidebar.columns(2)
        with col1:
            start_date_from = st.date_input(
                "Data Inizio Da",
                value=None,
                key="start_date_from"
            )
        with col2:
            start_date_to = st.date_input(
                "Data Inizio A",
                value=None,
                key="start_date_to"
            )

        if start_date_from or start_date_to:
            filters['data_inizio_range'] = (start_date_from, start_date_to)

        # Intervallo data fine
        col1, col2 = st.sidebar.columns(2)
        with col1:
            end_date_from = st.date_input(
                "Data Fine Da",
                value=None,
                key="end_date_from"
            )
        with col2:
            end_date_to = st.date_input(
                "Data Fine A",
                value=None,
                key="end_date_to"
            )

        if end_date_from or end_date_to:
            filters['data_fine_range'] = (end_date_from, end_date_to)

    # Pulsante cancella filtri
    if st.sidebar.button("Cancella Tutti i Filtri"):
        st.rerun()
    
    return filters

def render_kpi_section(kpis: dict):
    """Renderizza la sezione KPI con metriche corrette dei costi mensili di noleggio."""
    st.markdown("## 📊 Panoramica Costi Mensili Noleggio Flotta")
    st.markdown("*Nota: CANONE IMPONIBILE rappresenta i costi mensili di noleggio pagati dall'azienda.*")

    # KPI primari
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        create_kpi_card("Veicoli Totali", f"{kpis['total_vehicles']:,}")

    with col2:
        create_kpi_card("Veicoli Attivi", f"{kpis['active_vehicles']:,}", is_success=True)

    with col3:
        # Costi mensili flotta con visualizzazione IVA
        pre_tax = kpis['monthly_fleet_costs']
        with_vat = kpis['monthly_fleet_costs_with_vat']
        cost_display = f"€{pre_tax:,.0f} (€{with_vat:,.0f} con IVA)"
        create_kpi_card("Costi Mensili Flotta", cost_display, is_warning=True)

    with col4:
        create_kpi_card("In Scadenza", f"{kpis['expiring_soon']:,}", is_warning=kpis['expiring_soon'] > 0)

    # KPI secondari
    st.markdown("---")
    col5, col6, col7, col8 = st.columns(4)

    with col5:
        # Proiezione costi annuali con IVA (costi mensili × 12)
        annual_pre_tax = kpis['monthly_fleet_costs'] * 12
        annual_with_vat = kpis['monthly_fleet_costs_with_vat'] * 12
        annual_display = f"€{annual_pre_tax:,.0f} (€{annual_with_vat:,.0f} con IVA)"
        create_kpi_card("Proiezione Costi Annuali", annual_display, is_info=True)

    with col6:
        # Costo medio mensile per veicolo attivo
        avg_monthly_cost = kpis['monthly_fleet_costs'] / kpis['active_vehicles'] if kpis['active_vehicles'] > 0 else 0
        create_kpi_card("Costo Medio Mensile/Veicolo", f"€{avg_monthly_cost:.2f}", is_info=True)

    with col7:
        # Stato assicurazione
        create_kpi_card("Veicoli Assicurati", f"{kpis['insured_vehicles']:,}", is_success=True)

    with col8:
        # Veicoli non assicurati
        create_kpi_card("Veicoli Non Assicurati", f"{kpis['uninsured_vehicles']:,}", is_warning=kpis['uninsured_vehicles'] > 0)

def render_cost_savings_kpis(cost_savings: dict):
    """Renderizza la sezione KPI risparmi da consegne anticipate."""
    if cost_savings['vehicles_with_early_returns'] == 0:
        return  # Don't show section if no early returns

    st.markdown("## 💰 Risparmi da Consegne Anticipate")
    st.markdown("*Analisi dei risparmi ottenuti quando la data di consegna è precedente alla data di fine contratto*")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        # Total savings with VAT
        savings_pre_tax = cost_savings['total_savings']
        savings_with_vat = cost_savings['total_savings_with_vat']
        savings_display = f"€{savings_pre_tax:,.0f} (€{savings_with_vat:,.0f} con IVA)"
        create_kpi_card("Risparmi Totali", savings_display, is_success=True)

    with col2:
        # Vehicles with early returns
        create_kpi_card("Veicoli Consegnati in Anticipo", f"{cost_savings['vehicles_with_early_returns']:,}", is_info=True)

    with col3:
        # Total days saved
        create_kpi_card("Giorni Totali Risparmiati", f"{cost_savings['total_days_saved']:,}", is_info=True)

    with col4:
        # Average savings per vehicle
        avg_savings = cost_savings['avg_savings_per_vehicle']
        create_kpi_card("Risparmio Medio per Veicolo", f"€{avg_savings:,.0f}", is_success=True)

def render_expiration_impact_kpis(expiration_kpis: dict):
    """Renderizza la sezione KPI impatto scadenze."""
    st.markdown("## 📉 Impatto Costi Scadenze Noleggio")
    st.markdown("*Analisi delle riduzioni di costo dalle prossime scadenze di noleggio*")

    col1, col2, col3 = st.columns(3)

    with col1:
        # Riduzione costi prossimo mese con IVA
        next_month_pre_tax = expiration_kpis['next_month_cost_reduction']
        next_month_with_vat = expiration_kpis['next_month_cost_reduction_with_vat']
        next_month_display = f"€{next_month_pre_tax:,.0f} (€{next_month_with_vat:,.0f} con IVA)"
        create_kpi_card("Riduzione Costi Prossimo Mese", next_month_display, is_success=True)

    with col2:
        # Impatto costi Q1 con IVA
        q1_pre_tax = expiration_kpis['q1_cost_impact']
        q1_with_vat = expiration_kpis['q1_cost_impact_with_vat']
        q1_display = f"€{q1_pre_tax:,.0f} (€{q1_with_vat:,.0f} con IVA)"
        create_kpi_card("Impatto Costi T1", q1_display, is_info=True)

    with col3:
        # Risparmi annuali con IVA
        annual_pre_tax = expiration_kpis['annual_savings_from_expirations']
        annual_with_vat = expiration_kpis['annual_savings_from_expirations_with_vat']
        annual_display = f"€{annual_pre_tax:,.0f} (€{annual_with_vat:,.0f} con IVA)"
        create_kpi_card("Risparmi Annuali da Scadenze", annual_display, is_info=True)

def render_expiration_analysis(filtered_data: pd.DataFrame, data_handler: FleetDataHandler, chart_generator: ChartGenerator):
    """Renderizza la sezione di analisi dell'impatto dei costi delle scadenze di noleggio."""
    st.markdown("## 📊 Analisi Impatto Costi Scadenze Noleggio")
    st.markdown("*Timeline che mostra come i costi mensili della flotta diminuiranno alla scadenza dei noleggi*")

    # Ottieni dati timeline scadenze
    timeline_data = data_handler.get_expiration_timeline_data(filtered_data)

    if timeline_data.empty:
        st.warning("Nessuna scadenza di noleggio futura trovata nei dati filtrati correnti.")
        return

    # Crea e visualizza il grafico timeline
    st.markdown("### 📈 Timeline Riduzione Costi Mensili")
    fig = chart_generator.create_expiration_cost_timeline_chart(timeline_data)
    st.plotly_chart(fig, use_container_width=True)

    # Visualizza tabella interattiva scadenze
    st.markdown("### 📋 Tabella Impatto Scadenze")
    st.markdown("*Dettaglio delle riduzioni di costo per mese*")

    # Format the data for display
    display_data = timeline_data.copy()
    display_data['Monthly_Cost_Reduction'] = display_data['Monthly_Cost_Reduction'].apply(lambda x: f"€{x:,.2f}")
    if 'Monthly_Cost_Reduction_VAT' in display_data.columns:
        display_data['Monthly_Cost_Reduction_VAT'] = display_data['Monthly_Cost_Reduction_VAT'].apply(lambda x: f"€{x:,.2f}")
    display_data['Cumulative_Savings'] = display_data['Cumulative_Savings'].apply(lambda x: f"€{x:,.2f}")
    display_data['Remaining_Monthly_Cost'] = display_data['Remaining_Monthly_Cost'].apply(lambda x: f"€{x:,.2f}")
    display_data['Percentage_Reduction'] = display_data['Percentage_Reduction'].apply(lambda x: f"{x:.2f}%")

    # Rinomina colonne per una migliore visualizzazione
    display_data = display_data.rename(columns={
        'Month_Year': 'Mese Scadenza',
        'Vehicle_Count': 'Veicoli in Scadenza',
        'Monthly_Cost_Reduction': 'Riduzione Costi (Pre-tasse)',
        'Cumulative_Savings': 'Risparmi Cumulativi',
        'Remaining_Monthly_Cost': 'Costi Mensili Rimanenti',
        'Percentage_Reduction': 'Riduzione Percentuale'
    })

    # Seleziona colonne per la visualizzazione
    display_columns = ['Mese Scadenza', 'Veicoli in Scadenza', 'Riduzione Costi (Pre-tasse)',
                      'Risparmi Cumulativi', 'Costi Mensili Rimanenti', 'Riduzione Percentuale']

    # Configure AgGrid for the expiration table
    gb = GridOptionsBuilder.from_dataframe(display_data[display_columns])
    gb.configure_default_column(editable=False, groupable=False, sortable=True, filter=True)
    gb.configure_pagination(enabled=True, paginationPageSize=20)

    grid_options = gb.build()

    # Display the table
    AgGrid(
        display_data[display_columns],
        gridOptions=grid_options,
        height=400,
        fit_columns_on_grid_load=True,
        theme='streamlit'
    )

    # Statistiche riassuntive
    st.markdown("### 📊 Riepilogo Scadenze")
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_expiring = timeline_data['Vehicle_Count'].sum()
        st.metric("Totale Veicoli in Scadenza", f"{total_expiring:,}")

    with col2:
        total_cost_reduction = timeline_data['Monthly_Cost_Reduction'].sum()
        st.metric("Riduzione Totale Costi Mensili", f"€{total_cost_reduction:,.2f}")

    with col3:
        avg_monthly_reduction = timeline_data['Monthly_Cost_Reduction'].mean()
        st.metric("Riduzione Media Mensile", f"€{avg_monthly_reduction:,.2f}")

    with col4:
        final_monthly_cost = timeline_data['Remaining_Monthly_Cost'].iloc[-1] if len(timeline_data) > 0 else 0
        st.metric("Costo Mensile Finale", f"€{final_monthly_cost:,.2f}")

def render_cost_savings_analysis(filtered_data: pd.DataFrame, data_handler: FleetDataHandler):
    """Renderizza l'analisi dettagliata dei risparmi da consegne anticipate."""
    cost_savings = data_handler.calculate_cost_savings_from_early_returns(filtered_data)

    if cost_savings['vehicles_with_early_returns'] == 0:
        st.info("📊 Nessun veicolo con consegna anticipata trovato nei dati filtrati correnti.")
        return

    st.markdown("## 💰 Analisi Dettagliata Risparmi da Consegne Anticipate")
    st.markdown("*Dettaglio dei veicoli consegnati prima della data di fine contratto e relativi risparmi*")

    # Summary metrics
    col1, col2, col3 = st.columns(3)

    with col1:
        avg_days_saved = cost_savings['avg_days_saved_per_vehicle']
        st.metric("Media Giorni Risparmiati per Veicolo", f"{avg_days_saved:.1f}")

    with col2:
        total_monthly_cost_saved = cost_savings['total_savings']
        st.metric("Equivalente Costi Mensili Risparmiati", f"€{total_monthly_cost_saved:,.2f}")

    with col3:
        if cost_savings['total_days_saved'] > 0:
            daily_savings_rate = cost_savings['total_savings'] / cost_savings['total_days_saved']
            st.metric("Tasso Risparmio Giornaliero Medio", f"€{daily_savings_rate:.2f}")

    # Detailed table
    if not cost_savings['early_return_details'].empty:
        st.markdown("### 📋 Dettaglio Veicoli con Consegna Anticipata")

        # Show total count information
        total_early_returns = len(cost_savings['early_return_details'])
        st.info(f"📊 **Totale veicoli con consegna anticipata: {total_early_returns:,}** - Visualizzazione completa di tutti i veicoli")

        display_data = cost_savings['early_return_details'].copy()

        # Format columns for display
        display_data['DATA FINE'] = display_data['DATA FINE'].dt.strftime('%d/%m/%Y')
        display_data['DATA EFFETTIVA'] = display_data['DATA EFFETTIVA'].dt.strftime('%d/%m/%Y')
        display_data['CANONE IMPONIBILE'] = display_data['CANONE IMPONIBILE'].apply(lambda x: f"€{x:,.2f}")
        display_data['daily_cost'] = display_data['daily_cost'].apply(lambda x: f"€{x:.2f}")
        display_data['savings_per_vehicle'] = display_data['savings_per_vehicle'].apply(lambda x: f"€{x:,.2f}")

        # Rename columns for better display
        display_data = display_data.rename(columns={
            'TARGA': 'Targa',
            'MODELLO': 'Modello',
            'DATA FINE': 'Data Fine Contratto',
            'DATA EFFETTIVA': 'Data Consegna',
            'days_saved': 'Giorni Risparmiati',
            'CANONE IMPONIBILE': 'Canone Mensile',
            'daily_cost': 'Costo Giornaliero',
            'savings_per_vehicle': 'Risparmio Totale'
        })

        # Configure AgGrid for the savings table - OPTIMIZED FOR COMPLETE DATA DISPLAY
        gb = GridOptionsBuilder.from_dataframe(display_data)
        gb.configure_default_column(editable=False, groupable=False, sortable=True, filter=True)

        # Configure pagination to show more rows and allow users to see all data
        if total_early_returns <= 50:
            # For smaller datasets, show all rows without pagination
            gb.configure_pagination(enabled=False)
            table_height = min(600, 50 + (total_early_returns * 35))  # Dynamic height based on row count
        else:
            # For larger datasets, use larger page sizes with clear navigation
            gb.configure_pagination(enabled=True, paginationPageSize=50)  # Increased from 20 to 50
            table_height = 700  # Larger height for better visibility

        # Enable selection for potential future features
        gb.configure_selection(selection_mode="multiple", use_checkbox=True)

        # Add column sizing for better readability
        gb.configure_column("Targa", width=120, pinned="left")
        gb.configure_column("Modello", width=200)
        gb.configure_column("Data Fine Contratto", width=150)
        gb.configure_column("Data Consegna", width=150)
        gb.configure_column("Giorni Risparmiati", width=140, type=["numericColumn"])
        gb.configure_column("Canone Mensile", width=130, type=["numericColumn"])
        gb.configure_column("Costo Giornaliero", width=140, type=["numericColumn"])
        gb.configure_column("Risparmio Totale", width=140, type=["numericColumn"], pinned="right")

        grid_options = gb.build()

        # Display the table with enhanced configuration
        grid_response = AgGrid(
            display_data,
            gridOptions=grid_options,
            height=table_height,
            fit_columns_on_grid_load=True,
            theme='streamlit',
            allow_unsafe_jscode=False,
            update_mode=GridUpdateMode.SELECTION_CHANGED,
            data_return_mode=DataReturnMode.FILTERED_AND_SORTED
        )

        # Show additional statistics below the table
        st.markdown("---")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Veicoli Mostrati", f"{len(display_data):,}")

        with col2:
            total_savings_displayed = cost_savings['total_savings']
            st.metric("Risparmi Totali Mostrati", f"€{total_savings_displayed:,.2f}")

        with col3:
            avg_savings_displayed = total_savings_displayed / total_early_returns if total_early_returns > 0 else 0
            st.metric("Risparmio Medio per Veicolo", f"€{avg_savings_displayed:,.2f}")

        with col4:
            total_days_displayed = cost_savings['total_days_saved']
            st.metric("Giorni Totali Risparmiati", f"{total_days_displayed:,}")

        # Export functionality for complete dataset
        if st.button("📥 Esporta Tutti i Veicoli con Consegna Anticipata", key="export_early_returns"):
            # Prepare export data with original formatting
            export_data = cost_savings['early_return_details'].copy()
            export_data['DATA FINE'] = export_data['DATA FINE'].dt.strftime('%d/%m/%Y')
            export_data['DATA EFFETTIVA'] = export_data['DATA EFFETTIVA'].dt.strftime('%d/%m/%Y')

            csv_data = export_data.to_csv(index=False, encoding='utf-8-sig').encode('utf-8-sig')
            st.download_button(
                label="⬇️ Download CSV Completo",
                data=csv_data,
                file_name=f"veicoli_consegna_anticipata_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
                key="download_early_returns"
            )

def render_data_table(filtered_data: pd.DataFrame, data_handler: FleetDataHandler):
    """Render the interactive data table with memory optimizations."""
    st.markdown("## 📋 Fleet Data")

    if filtered_data.empty:
        st.warning("No data available with current filters.")
        return

    # Show data info
    st.info(f"📊 Displaying {len(filtered_data):,} rows")

    # Limit data for AgGrid to prevent memory issues
    MAX_ROWS_AGGRID = 5000
    display_data = filtered_data.head(MAX_ROWS_AGGRID).copy()

    if len(filtered_data) > MAX_ROWS_AGGRID:
        st.warning(f"⚠️ Showing first {MAX_ROWS_AGGRID:,} rows for performance. Use filters to narrow down results.")

    # Configure AgGrid with memory optimizations
    gb = GridOptionsBuilder.from_dataframe(display_data)

    # Optimize AgGrid configuration
    gb.configure_default_column(
        editable=True,
        groupable=False,  # Disable grouping to reduce memory
        sortable=True,
        filter=True,
        resizable=True,
        width=120  # Set default width to reduce rendering overhead
    )

    gb.configure_selection(selection_mode="multiple", use_checkbox=True)

    # Use manual pagination instead of auto to control memory
    gb.configure_pagination(
        enabled=True,
        paginationAutoPageSize=False,
        paginationPageSize=100  # Smaller page size for better performance
    )

    # Disable sidebar to reduce memory overhead
    # gb.configure_side_bar()  # Commented out for performance

    # Make certain columns non-editable
    non_editable_cols = ['TARGA', 'PROPRIETÀ']
    for col in non_editable_cols:
        if col in display_data.columns:
            gb.configure_column(col, editable=False)

    grid_options = gb.build()

    # Display grid with optimized settings
    grid_response = AgGrid(
        display_data,
        gridOptions=grid_options,
        data_return_mode=DataReturnMode.AS_INPUT,  # More efficient than FILTERED_AND_SORTED
        update_mode=GridUpdateMode.VALUE_CHANGED,  # More efficient than MODEL_CHANGED
        fit_columns_on_grid_load=False,  # Disable to reduce initial load time
        height=400,  # Reduced height for better performance
        allow_unsafe_jscode=False,  # Disable for security and performance
        reload_data=False,  # Prevent unnecessary reloads
        theme='streamlit'  # Use streamlit theme for consistency
    )
    
    # Handle data updates and export
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("💾 Save Changes", type="primary"):
            if grid_response['data'].shape[0] > 0:
                updated_data = pd.DataFrame(grid_response['data'])
                if data_handler.save_data(updated_data):
                    st.success("Data saved successfully!")
                    st.rerun()
                else:
                    st.error("Failed to save data.")
            else:
                st.warning("No data to save.")

    with col2:
        # Export displayed data
        if st.button("📥 Export Displayed Data"):
            csv_data = data_handler.export_filtered_data(display_data, "displayed_fleet_data.csv")
            st.download_button(
                label="⬇️ Download CSV",
                data=csv_data,
                file_name=f"displayed_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
                key="download_displayed"
            )

    with col3:
        # Export all filtered data (potentially large)
        if st.button("📥 Export All Filtered Data"):
            if len(filtered_data) > 10000:
                st.warning("⚠️ Large dataset - this may take a moment...")

            csv_data = data_handler.export_filtered_data(filtered_data, "all_filtered_data.csv")
            st.download_button(
                label="⬇️ Download All CSV",
                data=csv_data,
                file_name=f"all_filtered_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
                key="download_all"
            )

def render_charts(filtered_data: pd.DataFrame, chart_generator: ChartGenerator):
    """Render the charts section."""
    st.markdown("## 📈 Analytics")
    
    # First row of charts
    col1, col2 = st.columns(2)
    
    with col1:
        fig1 = chart_generator.create_vehicles_by_category_chart(filtered_data)
        st.plotly_chart(fig1, use_container_width=True)
    
    with col2:
        fig2 = chart_generator.create_revenue_comparison_chart(filtered_data)
        st.plotly_chart(fig2, use_container_width=True)
    
    # Second row of charts
    col1, col2 = st.columns(2)
    
    with col1:
        fig3 = chart_generator.create_lease_timeline_chart(filtered_data)
        st.plotly_chart(fig3, use_container_width=True)
    
    with col2:
        fig4 = chart_generator.create_status_distribution_chart(filtered_data)
        st.plotly_chart(fig4, use_container_width=True)
    
    # Third row - location chart
    fig5 = chart_generator.create_location_chart(filtered_data)
    st.plotly_chart(fig5, use_container_width=True)

def main():
    """Main application function."""
    # Intestazione
    st.markdown("""
    <div class="main-header">
        <h1>🚗 Dashboard Costi Mensili Noleggio Flotta</h1>
        <p>Sistema di analisi e gestione dei costi mensili di noleggio della flotta veicoli</p>
        <small><em>📊 CANONE IMPONIBILE rappresenta i costi mensili di noleggio pagati dall'azienda</em></small>
    </div>
    """, unsafe_allow_html=True)
    
    # Load data
    data_handler = load_data()
    if data_handler is None:
        st.error("Failed to load data. Please check the CSV file.")
        return
    
    st.session_state.data_handler = data_handler
    
    # Sidebar filters
    filters = render_sidebar_filters(data_handler)
    
    # Apply filters
    filtered_data = data_handler.apply_filters(filters)
    st.session_state.filtered_data = filtered_data
    
    # Calculate KPIs
    kpis = data_handler.calculate_kpis(filtered_data)
    expiration_kpis = data_handler.calculate_expiration_impact_kpis(filtered_data)
    cost_savings = data_handler.calculate_cost_savings_from_early_returns(filtered_data)

    # Render sections
    render_kpi_section(kpis)
    render_cost_savings_kpis(cost_savings)
    render_expiration_impact_kpis(expiration_kpis)

    # Crea schede per una migliore organizzazione
    tab1, tab2, tab3, tab4 = st.tabs(["📋 Tabella Dati", "📈 Analisi", "📉 Analisi Scadenze", "💰 Risparmi Consegne"])

    with tab1:
        render_data_table(filtered_data, data_handler)

    with tab2:
        render_charts(filtered_data, chart_generator)

    with tab3:
        render_expiration_analysis(filtered_data, data_handler, chart_generator)

    with tab4:
        render_cost_savings_analysis(filtered_data, data_handler)

    # Piè di pagina
    st.markdown("---")
    st.markdown("*Dashboard Costi Mensili Noleggio Flotta - Sviluppata con Streamlit & Plotly*")
    st.markdown("**Contesto Dati**: Questa dashboard analizza i costi mensili di noleggio veicoli e lo stato della flotta. CANONE IMPONIBILE e CANONE IVATO rappresentano i costi mensili di noleggio pagati dall'azienda.")

if __name__ == "__main__":
    main()