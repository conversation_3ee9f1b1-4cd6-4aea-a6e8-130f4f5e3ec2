# 🤖 Configurazione Ollama per Fleet Sight Dashboard

## ✅ Stato Attuale
- **Ollama**: ✅ Installato e funzionante
- **Modello**: ✅ llama3.2:latest disponibile
- **Servizio**: ✅ In esecuzione su porta 11434
- **Dashboard**: ✅ Configurato per utilizzare Ollama

## 🚀 Funzionalità AI Abilitate

### Analisi Intelligente della Flotta
- **Analisi prestazioni**: Insights automatici sui KPI della flotta
- **Ottimizzazione costi**: Identificazione opportunità di risparmio
- **Previsioni**: Analytics predittive sui trend futuri
- **Raccomandazioni**: Suggerimenti strategici basati sui dati

### Interfaccia Chat AI
- **Chat interattiva**: Domande in linguaggio naturale sui dati
- **Risposte contestuali**: Analisi specifica per ogni pagina del dashboard
- **Prompt rapidi**: Domande predefinite per analisi comuni
- **Supporto italiano**: <PERSON><PERSON> le risposte in italiano

## 🔧 Comandi Utili

### Gestione Ollama
```bash
# Avviare Ollama
ollama serve

# Verificare modelli installati
ollama list

# Installare un nuovo modello
ollama pull llama3.2

# Testare un modello
ollama run llama3.2
```

### Verifica Connessione
```bash
# Test API Ollama
curl http://localhost:11434/api/tags

# Test generazione
curl -X POST http://localhost:11434/api/generate \
  -H "Content-Type: application/json" \
  -d '{"model": "llama3.2:latest", "prompt": "Ciao!", "stream": false}'
```

## 📊 Come Utilizzare l'AI nel Dashboard

### 1. Accesso Chat AI
- Clicca sull'icona AI in qualsiasi pagina del dashboard
- La chat si aprirà con un messaggio di benvenuto
- Status "🟢 Connesso" indica che l'AI è attiva

### 2. Tipi di Domande
**Analisi Generale:**
- "Analizza le prestazioni della flotta"
- "Mostra opportunità di risparmio"
- "Quali sono i trend principali?"

**Costi e Ottimizzazione:**
- "Come ridurre i costi mensili?"
- "Analizza i risparmi dalle consegne anticipate"
- "Veicoli con costi elevati"

**Analisi Specifica:**
- "Distribuzione veicoli per categoria"
- "Analisi utilizzo per località"
- "Veicoli sottoutilizzati"

### 3. Prompt Rapidi
Ogni pagina del dashboard ha prompt predefiniti:
- **Overview**: Analisi generale e KPI
- **Costi**: Ottimizzazione e risparmi
- **Flotta**: Distribuzione e utilizzo

## ⚙️ Configurazione Avanzata

### Modelli Alternativi
```bash
# Modelli più leggeri
ollama pull llama3.2:1b    # 1.3GB
ollama pull phi3:mini      # 2.3GB

# Modelli più potenti
ollama pull llama3.1:8b    # 4.7GB
ollama pull mistral:latest # 4.1GB
```

### Personalizzazione
Modifica `src/config/ai.config.ts` per:
- Cambiare modello predefinito
- Regolare temperatura (creatività)
- Modificare prompt templates
- Personalizzare messaggi di sistema

### Performance
```bash
# Monitoraggio risorse
htop
nvidia-smi  # Se hai GPU NVIDIA

# Ottimizzazione memoria
export OLLAMA_NUM_PARALLEL=1
export OLLAMA_MAX_LOADED_MODELS=1
```

## 🐛 Risoluzione Problemi

### AI Non Disponibile
1. **Verifica Ollama**:
   ```bash
   ollama serve
   ```

2. **Controlla porta**:
   ```bash
   lsof -i :11434
   ```

3. **Riavvia servizio**:
   ```bash
   pkill ollama
   ollama serve
   ```

### Risposte Lente
1. **Riduci temperatura**: Modifica `ai.config.ts`
2. **Usa modello più leggero**: `ollama pull llama3.2:1b`
3. **Limita token**: Riduci `maxTokens` in configurazione

### Errori di Connessione
1. **Firewall**: Verifica che porta 11434 sia aperta
2. **Proxy**: Configura bypass per localhost
3. **CORS**: Ollama gestisce automaticamente CORS per localhost

## 📈 Monitoraggio

### Logs Ollama
```bash
# Logs in tempo reale
tail -f ~/.ollama/logs/server.log

# Verifica utilizzo GPU
ollama ps
```

### Metriche Dashboard
- Tempo risposta AI
- Numero query giornaliere
- Modelli utilizzati
- Errori di connessione

## 🔄 Aggiornamenti

### Aggiornare Ollama
```bash
# macOS
brew upgrade ollama

# Linux
curl -fsSL https://ollama.ai/install.sh | sh
```

### Aggiornare Modelli
```bash
ollama pull llama3.2:latest
```

## 💡 Best Practices

1. **Domande Specifiche**: Più dettagli = risposte migliori
2. **Contesto**: Specifica il periodo temporale o categoria
3. **Iterazione**: Fai domande di follow-up per approfondire
4. **Feedback**: Usa i risultati per affinare le domande successive

## 🎯 Prossimi Passi

- [ ] Testare diverse tipologie di domande
- [ ] Esplorare prompt rapidi per ogni sezione
- [ ] Configurare modelli alternativi se necessario
- [ ] Monitorare performance e ottimizzare

---

**🎉 La tua configurazione Ollama è completa e pronta per l'uso!**

Per supporto: controlla i logs o riavvia il servizio Ollama.
