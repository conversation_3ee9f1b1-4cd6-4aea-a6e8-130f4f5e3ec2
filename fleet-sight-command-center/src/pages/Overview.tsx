import { Car, DollarSign, AlertTriangle, TrendingUp } from "lucide-react";
import { KPICard } from "@/components/dashboard/KPICard";
import { WidgetGrid } from "@/components/dashboard/WidgetGrid";
import { Breadcrumbs } from "@/components/layout/Breadcrumbs";

export default function Overview() {
  const kpiData = [
    {
      title: "Active Vehicles",
      value: "2,847",
      subtitle: "Total fleet size",
      trend: {
        value: 5.2,
        label: "vs last month",
        type: "positive" as const,
      },
      icon: <Car className="w-4 h-4" />,
    },
    {
      title: "Monthly Costs",
      value: "$1.24M",
      subtitle: "Total operational costs",
      trend: {
        value: -3.1,
        label: "vs last month",
        type: "positive" as const,
      },
      icon: <DollarSign className="w-4 h-4" />,
    },
    {
      title: "Maintenance Alerts",
      value: "47",
      subtitle: "Vehicles requiring service",
      trend: {
        value: 12.5,
        label: "vs last month",
        type: "negative" as const,
      },
      icon: <AlertTriangle className="w-4 h-4" />,
    },
    {
      title: "Fleet Efficiency",
      value: "87.3%",
      subtitle: "Average utilization rate",
      trend: {
        value: 2.8,
        label: "vs last month",
        type: "positive" as const,
      },
      icon: <TrendingUp className="w-4 h-4" />,
    },
  ];

  return (
    <div className="space-y-6">
      <Breadcrumbs />
      
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Fleet Overview</h1>
          <p className="text-muted-foreground">
            Monitor your fleet performance and key metrics at a glance
          </p>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {kpiData.map((kpi, index) => (
            <KPICard
              key={index}
              title={kpi.title}
              value={kpi.value}
              subtitle={kpi.subtitle}
              trend={kpi.trend}
              icon={kpi.icon}
            />
          ))}
        </div>

        {/* Widget Grid */}
        <WidgetGrid />
      </div>
    </div>
  );
}