import React, { useState, useEffect } from 'react';
import { Breadcrumbs } from "@/components/layout/Breadcrumbs";
import { FleetKPICard, FleetCostKPI, SavingsKPI, FleetKPIGrid } from "@/components/dashboard/FleetKPICard";
import { FleetFilterPanel } from "@/components/filters/FleetFilterPanel";
import { WidgetGrid } from "@/components/dashboard/WidgetGrid";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Car,
  DollarSign,
  TrendingUp,
  Shield,
  Calendar,
  Zap,
  AlertTriangle,
  BarChart3,
  FilterIcon
} from 'lucide-react';
import fleetDataService, { FilterOptions, FleetKPIs, CostSavingsAnalysis } from '@/services/fleetDataService';
import enhancedFleetDataService from '@/services/enhancedFleetDataService';
import AIChatInterface from '@/components/ai/AIChatInterface';

export default function Overview() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterOptions>({});
  const [showFilters, setShowFilters] = useState(false);
  const [kpis, setKpis] = useState<FleetKPIs | null>(null);
  const [costSavings, setCostSavings] = useState<CostSavingsAnalysis | null>(null);
  const [showChatAI, setShowChatAI] = useState(false);
  const [availableOptions, setAvailableOptions] = useState({
    stato: [] as string[],
    categoria: [] as string[],
    gruppo: [] as string[],
    proprieta: [] as string[],
    parcoSede: [] as string[],
    assicurazione: [] as string[],
  });

  useEffect(() => {
    loadFleetData();
  }, []);

  useEffect(() => {
    if (!isLoading) {
      updateDashboardData();
    }
  }, [filters, isLoading]);

  const loadFleetData = async () => {
    try {
      setIsLoading(true);

      // Load CSV data
      const response = await fetch('/data/RegistroFLOTTA_Master.csv');
      const csvContent = await response.text();

      await fleetDataService.loadData(csvContent);
      // Also load into enhanced service for better analytics
      await enhancedFleetDataService.loadData(csvContent);

      // Get available filter options
      setAvailableOptions({
        stato: fleetDataService.getUniqueValues('stato'),
        categoria: fleetDataService.getUniqueValues('categoria'),
        gruppo: fleetDataService.getUniqueValues('gruppo'),
        proprieta: fleetDataService.getUniqueValues('proprieta'),
        parcoSede: fleetDataService.getUniqueValues('parcoSede'),
        assicurazione: fleetDataService.getUniqueValues('assicurazione'),
      });

      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load fleet data');
      console.error('Error loading fleet data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const updateDashboardData = () => {
    try {
      const newKpis = fleetDataService.calculateKPIs(filters);
      const newCostSavings = fleetDataService.calculateCostSavings(filters);

      setKpis(newKpis);
      setCostSavings(newCostSavings);
    } catch (err) {
      console.error('Error updating dashboard data:', err);
    }
  };

  const handleFiltersChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Breadcrumbs />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Caricamento dati flotta...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Breadcrumbs />
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Errore nel caricamento dei dati</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={loadFleetData}>Riprova</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Breadcrumbs />

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">🚗 Fleet Management Dashboard</h1>
            <p className="text-muted-foreground">
              Panoramica completa della flotta e analisi dei costi
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              onClick={() => setShowChatAI(!showChatAI)}
              className="flex items-center gap-2"
            >
              <Zap className="h-4 w-4" />
              AI Assistant
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <FilterIcon className="h-4 w-4" />
              Filtri
              {Object.keys(filters).length > 0 && (
                <span className="bg-primary text-primary-foreground rounded-full px-2 py-1 text-xs">
                  {Object.keys(filters).length}
                </span>
              )}
            </Button>
          </div>
        </div>

        {/* Main KPIs */}
        {kpis && (
          <FleetKPIGrid columns={4}>
            <FleetKPICard
              title="Veicoli Totali"
              value={kpis.totalVehicles}
              subtitle={`${kpis.activeVehicles} attivi`}
              icon={Car}
              trend={{
                type: 'neutral',
                value: `${((kpis.activeVehicles / kpis.totalVehicles) * 100).toFixed(1)}%`,
                label: 'Tasso di utilizzo'
              }}
            />

            <FleetCostKPI
              title="Costi Mensili Totali"
              preTaxAmount={kpis.totalMonthlyCost}
              withVATAmount={kpis.totalMonthlyCostWithVAT}
              icon={DollarSign}
              trend={{
                type: 'neutral',
                value: `€${kpis.averageCostPerVehicle.toFixed(0)}/veicolo`
              }}
            />

            <FleetKPICard
              title="Tasso di Utilizzo"
              value={kpis.utilizationRate}
              subtitle={`${kpis.activeVehicles} di ${kpis.totalVehicles} veicoli`}
              icon={TrendingUp}
              format="percentage"
              trend={{
                type: kpis.utilizationRate > 80 ? 'positive' : kpis.utilizationRate > 60 ? 'neutral' : 'negative',
                value: kpis.utilizationRate > 80 ? 'Ottimo' : kpis.utilizationRate > 60 ? 'Buono' : 'Da migliorare'
              }}
            />

            <FleetKPICard
              title="Copertura Assicurativa"
              value={kpis.insuranceRate}
              subtitle={`${kpis.vehiclesWithInsurance} veicoli assicurati`}
              icon={Shield}
              format="percentage"
              trend={{
                type: kpis.insuranceRate > 95 ? 'positive' : kpis.insuranceRate > 80 ? 'neutral' : 'negative',
                value: kpis.insuranceRate > 95 ? 'Completa' : kpis.insuranceRate > 80 ? 'Buona' : 'Attenzione'
              }}
            />
          </FleetKPIGrid>
        )}

        {/* Cost Savings Analysis */}
        {costSavings && costSavings.vehiclesWithEarlyReturns > 0 && (
          <div>
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <Zap className="h-5 w-5 text-green-600" />
              Analisi Risparmi da Consegne Anticipate
            </h2>
            <FleetKPIGrid columns={3}>
              <SavingsKPI
                title="Risparmi Totali"
                totalSavings={costSavings.totalSavings}
                totalSavingsWithVAT={costSavings.totalSavingsWithVAT}
                vehicleCount={costSavings.vehiclesWithEarlyReturns}
                daysSaved={costSavings.totalDaysSaved}
                icon={DollarSign}
              />

              <FleetKPICard
                title="Veicoli con Consegna Anticipata"
                value={costSavings.vehiclesWithEarlyReturns}
                subtitle={`Media ${costSavings.averageDaysSavedPerVehicle.toFixed(1)} giorni risparmiati`}
                icon={Calendar}
                trend={{
                  type: 'positive',
                  value: `${costSavings.totalDaysSaved} giorni`,
                  label: 'Totale giorni risparmiati'
                }}
              />

              <FleetKPICard
                title="Risparmio Medio per Veicolo"
                value={costSavings.averageSavingsPerVehicle}
                subtitle="Per veicolo con consegna anticipata"
                icon={BarChart3}
                format="currency"
                trend={{
                  type: 'positive',
                  value: 'Ottimizzazione',
                  label: 'Gestione efficiente'
                }}
              />
            </FleetKPIGrid>
          </div>
        )}

        {/* Widgets Grid */}
        <WidgetGrid />
      </div>

      {/* Filter Panel */}
      <FleetFilterPanel
        filters={filters}
        onFiltersChange={handleFiltersChange}
        availableOptions={availableOptions}
        isOpen={showFilters}
        onToggle={() => setShowFilters(!showFilters)}
      />

      {/* AI Chat Interface */}
      <AIChatInterface 
        isOpen={showChatAI}
        onToggle={() => setShowChatAI(!showChatAI)}
        context="overview"
      />
    </div>
  );
}