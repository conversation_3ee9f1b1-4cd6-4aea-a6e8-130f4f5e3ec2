import React, { useState, useEffect } from 'react';
import { Breadcrumbs } from "@/components/layout/Breadcrumbs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  Pie<PERSON>hart, Pie, Cell, BarChart, Bar, LineChart, Line, 
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  ComposedChart, Area, AreaChart, ScatterChart, Scatter
} from 'recharts';
import {
  Car, MapPin, TrendingUp, Calendar, DollarSign, Users,
  PieChart as PieChartIcon, BarChart3, Activity, Target,
  Clock, AlertTriangle, Zap, Settings, Filter
} from 'lucide-react';
import enhancedFleetDataService from '@/services/enhancedFleetDataService';
import { FleetAnalytics, AdvancedFilterOptions } from '@/types/fleet';
import AIChatInterface from '@/components/ai/AIChatInterface';

const CHART_COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff7f',
  '#dc143c', '#00bfff', '#ff69b4', '#32cd32', '#ff4500'
];

export default function EnhancedFleetAnalysis() {
  const [analytics, setAnalytics] = useState<FleetAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<AdvancedFilterOptions>({});
  const [showChatAI, setShowChatAI] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadAnalytics();
  }, [filters]);

  const loadAnalytics = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load data if not already loaded
      if (!enhancedFleetDataService.getVehicles().length) {
        const response = await fetch('/data/RegistroFLOTTA_Master.csv');
        const csvContent = await response.text();
        await enhancedFleetDataService.loadData(csvContent);
      }

      const newAnalytics = enhancedFleetDataService.calculateComprehensiveAnalytics(filters);
      setAnalytics(newAnalytics);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load fleet analytics');
      console.error('Error loading analytics:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('it-IT').format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Breadcrumbs />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Caricamento analisi flotta...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Breadcrumbs />
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Errore nel caricamento</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={loadAnalytics}>Riprova</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!analytics) return null;

  return (
    <div className="space-y-6">
      <Breadcrumbs />
      
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">🚗 Analisi Flotta Avanzata</h1>
          <p className="text-muted-foreground">
            Analisi approfondita della composizione, performance e costi della flotta
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setShowChatAI(!showChatAI)}
            className="flex items-center gap-2"
          >
            <Zap className="h-4 w-4" />
            AI Assistant
          </Button>
          <Badge variant="secondary">
            {formatNumber(analytics.totalVehicles)} veicoli
          </Badge>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Utilizzo Flotta</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(analytics.utilizationRate)}</div>
            <Progress value={analytics.utilizationRate} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-2">
              {formatNumber(analytics.activeVehicles)} di {formatNumber(analytics.totalVehicles)} attivi
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Costo Medio</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analytics.averageCostPerVehicle)}</div>
            <p className="text-xs text-muted-foreground mt-2">
              Per veicolo/mese
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Copertura Assicurativa</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analytics.totalInsuranceCoverage)}</div>
            <p className="text-xs text-muted-foreground mt-2">
              Totale copertura
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contratti in Scadenza</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {formatNumber(analytics.expiringContracts.length)}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Prossimi 30 giorni
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <PieChartIcon className="h-4 w-4" />
            Composizione
          </TabsTrigger>
          <TabsTrigger value="locations" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Località
          </TabsTrigger>
          <TabsTrigger value="costs" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Costi
          </TabsTrigger>
          <TabsTrigger value="contracts" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Contratti
          </TabsTrigger>
          <TabsTrigger value="optimization" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Ottimizzazione
          </TabsTrigger>
        </TabsList>

        {/* Fleet Composition Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Category Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Distribuzione per Categoria</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={analytics.categoryBreakdown}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ categoria, percentage }) => `${categoria} (${percentage.toFixed(1)}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {analytics.categoryBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any, name: any, props: any) => [
                      `${formatNumber(value)} veicoli`,
                      props.payload.categoria
                    ]} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Group Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle>Distribuzione per Gruppo</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={analytics.groupBreakdown}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="gruppo" />
                    <YAxis />
                    <Tooltip formatter={(value: any) => formatNumber(value)} />
                    <Bar dataKey="count" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Status and Transmission */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Status Veicoli</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.statusBreakdown.map((status, index) => (
                    <div key={status.stato} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div 
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: CHART_COLORS[index % CHART_COLORS.length] }}
                        />
                        <span className="font-medium">{status.stato}</span>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">{formatNumber(status.count)}</div>
                        <div className="text-sm text-muted-foreground">
                          {formatPercentage(status.percentage)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Tipo Trasmissione</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={analytics.transmissionBreakdown}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="count"
                    >
                      {analytics.transmissionBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any, name: any, props: any) => [
                      `${formatNumber(value)} veicoli`,
                      props.payload.trasmissione
                    ]} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Locations Tab */}
        <TabsContent value="locations" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top 10 Località per Numero Veicoli</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={analytics.topLocations} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="location" type="category" width={120} />
                    <Tooltip formatter={(value: any) => formatNumber(value)} />
                    <Bar dataKey="vehicleCount" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Analisi Utilizzo per Località</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <ScatterChart data={analytics.locationBreakdown.slice(0, 20)}>
                    <CartesianGrid />
                    <XAxis dataKey="vehicleCount" name="Numero Veicoli" />
                    <YAxis dataKey="utilizationRate" name="Tasso Utilizzo %" />
                    <Tooltip 
                      cursor={{ strokeDasharray: '3 3' }}
                      formatter={(value: any, name: any) => [
                        name === 'vehicleCount' ? formatNumber(value) : formatPercentage(value),
                        name === 'vehicleCount' ? 'Veicoli' : 'Utilizzo'
                      ]}
                      labelFormatter={(label: any, payload: any) => 
                        payload?.[0]?.payload?.location || 'Località'
                      }
                    />
                    <Scatter dataKey="utilizationRate" fill="#82ca9d" />
                  </ScatterChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Costs Tab */}
        <TabsContent value="costs" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Analisi Costi per Categoria</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={400}>
                  <ComposedChart data={analytics.categoryBreakdown}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="categoria" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip 
                      formatter={(value: any, name: any) => [
                        name === 'count' ? formatNumber(value) : formatCurrency(value),
                        name === 'count' ? 'Veicoli' : name === 'totalCost' ? 'Costo Totale' : 'Costo Medio'
                      ]}
                    />
                    <Legend />
                    <Bar yAxisId="left" dataKey="count" fill="#8884d8" name="Numero Veicoli" />
                    <Line yAxisId="right" type="monotone" dataKey="averageCost" stroke="#ff7300" name="Costo Medio" />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Distribuzione Costi per Proprietario</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={analytics.ownerBreakdown.slice(0, 10)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="proprieta" angle={-45} textAnchor="end" height={100} />
                    <YAxis />
                    <Tooltip formatter={(value: any) => formatCurrency(value)} />
                    <Bar dataKey="totalCost" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Contracts Tab */}
        <TabsContent value="contracts" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Durata Contratti</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Breve termine (&le;12 mesi)</span>
                    <Badge>{formatNumber(analytics.contractDurationStats.shortTermContracts)}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Medio termine (13-36 mesi)</span>
                    <Badge variant="secondary">{formatNumber(analytics.contractDurationStats.mediumTermContracts)}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Lungo termine (&gt;36 mesi)</span>
                    <Badge variant="outline">{formatNumber(analytics.contractDurationStats.longTermContracts)}</Badge>
                  </div>
                  <div className="pt-2 border-t">
                    <div className="flex items-center justify-between font-semibold">
                      <span>Durata Media</span>
                      <span>{analytics.contractDurationStats.averageDuration.toFixed(1)} mesi</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Tipo Acquisizione</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={analytics.acquisitionTypeBreakdown}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ acquisizione, percentage }) => `${acquisizione} (${percentage.toFixed(1)}%)`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {analytics.acquisitionTypeBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any) => formatNumber(value)} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Expiring Contracts Table */}
          {analytics.expiringContracts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Contratti in Scadenza (Prossimi 30 giorni)</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {analytics.expiringContracts.slice(0, 10).map((contract, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">{contract.targa}</div>
                        <div className="text-sm text-muted-foreground">{contract.modello}</div>
                        <div className="text-sm text-muted-foreground">{contract.location}</div>
                      </div>
                      <div className="text-right">
                        <Badge variant={contract.daysUntilExpiration <= 7 ? 'destructive' : 'secondary'}>
                          {contract.daysUntilExpiration} giorni
                        </Badge>
                        <div className="text-sm text-muted-foreground mt-1">
                          {formatCurrency(contract.monthlyCost)}/mese
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Optimization Tab */}
        <TabsContent value="optimization" className="space-y-6">
          <div className="grid gap-6">
            {/* Cost Savings Analysis */}
            <Card>
              <CardHeader>
                <CardTitle>Analisi Risparmi da Consegne Anticipate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">
                      {formatCurrency(analytics.costSavingsAnalysis.totalSavings)}
                    </div>
                    <div className="text-sm text-muted-foreground">Risparmi Totali</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold">
                      {formatNumber(analytics.costSavingsAnalysis.vehiclesWithEarlyReturns)}
                    </div>
                    <div className="text-sm text-muted-foreground">Veicoli con Consegna Anticipata</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold">
                      {formatNumber(analytics.costSavingsAnalysis.totalDaysSaved)}
                    </div>
                    <div className="text-sm text-muted-foreground">Giorni Totali Risparmiati</div>
                  </div>
                </div>
                
                {analytics.earlyReturns.savingsByLocation.length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-semibold mb-4">Risparmi per Località</h4>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={analytics.earlyReturns.savingsByLocation.slice(0, 10)}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="location" angle={-45} textAnchor="end" height={100} />
                        <YAxis />
                        <Tooltip formatter={(value: any) => formatCurrency(value)} />
                        <Bar dataKey="totalSavings" fill="#10b981" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* AI Chat Interface */}
      <AIChatInterface 
        isOpen={showChatAI}
        onToggle={() => setShowChatAI(!showChatAI)}
        context="fleet"
      />
    </div>
  );
}