import React, { useState, useEffect } from 'react';
import { Breadcrumbs } from "@/components/layout/Breadcrumbs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger,
} from "@/components/ui/dialog";
import {
  <PERSON><PERSON>s, Bot, Database, Palette, Bell, Shield, Globe, 
  CheckCircle, AlertCircle, Info, Download, Upload, RefreshCw,
  Brain, Zap, Server, Monitor, HardDrive, Wifi, WifiOff
} from 'lucide-react';
import aiChatService from '@/services/aiChatService';
import { FleetSettings, UserPreferences } from '@/types/fleet';

export default function EnhancedSettings() {
  const [settings, setSettings] = useState<FleetSettings>({
    dataRefreshInterval: 30,
    defaultCurrency: 'EUR',
    defaultDateFormat: 'DD/MM/YYYY',
    enableAI: false,
    aiProvider: 'ollama',
    ollamaEndpoint: 'http://localhost:11434',
    ollamaModel: 'llama2',
    enableNotifications: true,
    thresholds: {
      contractExpirationWarningDays: 30,
      highCostAlert: 1000,
      lowUtilizationAlert: 70,
    },
  });

  const [preferences, setPreferences] = useState<UserPreferences>({
    theme: 'system',
    language: 'it',
    favoriteCharts: [],
    dashboardLayout: 'default',
    defaultFilters: {},
  });

  const [aiStatus, setAiStatus] = useState<{
    connected: boolean;
    models: string[];
    error?: string;
  }>({
    connected: false,
    models: [],
  });

  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  useEffect(() => {
    loadSettings();
    checkAIStatus();
  }, []);

  const loadSettings = () => {
    // Load from localStorage or API
    const savedSettings = localStorage.getItem('fleetSettings');
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings));
    }

    const savedPreferences = localStorage.getItem('userPreferences');
    if (savedPreferences) {
      setPreferences(JSON.parse(savedPreferences));
    }
  };

  const saveSettings = () => {
    localStorage.setItem('fleetSettings', JSON.stringify(settings));
    localStorage.setItem('userPreferences', JSON.stringify(preferences));
    setHasUnsavedChanges(false);
    
    // Apply AI settings
    if (settings.enableAI) {
      aiChatService.updateConfig({
        endpoint: settings.ollamaEndpoint,
        model: settings.ollamaModel,
        enabled: settings.enableAI,
      });
    }
  };

  const checkAIStatus = async () => {
    try {
      const status = await aiChatService.checkOllamaStatus();
      setAiStatus({
        connected: status.available,
        models: status.models || [],
        error: status.error,
      });
    } catch (error) {
      setAiStatus({
        connected: false,
        models: [],
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  const testAIConnection = async () => {
    setIsTestingConnection(true);
    try {
      await checkAIStatus();
      if (settings.enableAI) {
        await aiChatService.initialize({
          endpoint: settings.ollamaEndpoint,
          model: settings.ollamaModel,
          enabled: true,
        });
      }
    } finally {
      setIsTestingConnection(false);
    }
  };

  const updateSetting = (key: keyof FleetSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasUnsavedChanges(true);
  };

  const updateThreshold = (key: keyof FleetSettings['thresholds'], value: number) => {
    setSettings(prev => ({
      ...prev,
      thresholds: { ...prev.thresholds, [key]: value }
    }));
    setHasUnsavedChanges(true);
  };

  const updatePreference = (key: keyof UserPreferences, value: any) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
    setHasUnsavedChanges(true);
  };

  const exportSettings = () => {
    const settingsExport = {
      settings,
      preferences,
      exportDate: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(settingsExport, null, 2)], {
      type: 'application/json',
    });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `fleet_settings_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const importSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string);
        if (imported.settings) setSettings(imported.settings);
        if (imported.preferences) setPreferences(imported.preferences);
        setHasUnsavedChanges(true);
      } catch (error) {
        console.error('Error importing settings:', error);
      }
    };
    reader.readAsText(file);
  };

  const resetToDefaults = () => {
    setSettings({
      dataRefreshInterval: 30,
      defaultCurrency: 'EUR',
      defaultDateFormat: 'DD/MM/YYYY',
      enableAI: false,
      aiProvider: 'ollama',
      ollamaEndpoint: 'http://localhost:11434',
      ollamaModel: 'llama2',
      enableNotifications: true,
      thresholds: {
        contractExpirationWarningDays: 30,
        highCostAlert: 1000,
        lowUtilizationAlert: 70,
      },
    });
    
    setPreferences({
      theme: 'system',
      language: 'it',
      favoriteCharts: [],
      dashboardLayout: 'default',
      defaultFilters: {},
    });
    
    setHasUnsavedChanges(true);
  };

  return (
    <div className="space-y-6">
      <Breadcrumbs />
      
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">⚙️ Impostazioni</h1>
          <p className="text-muted-foreground">
            Configura le impostazioni del sistema, AI e preferenze utente
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {hasUnsavedChanges && (
            <Badge variant="outline" className="text-orange-600">
              Modifiche non salvate
            </Badge>
          )}
          <Button onClick={saveSettings} disabled={!hasUnsavedChanges}>
            Salva Impostazioni
          </Button>
        </div>
      </div>

      <Tabs defaultValue="ai" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="ai" className="flex items-center gap-2">
            <Bot className="h-4 w-4" />
            AI & Ollama
          </TabsTrigger>
          <TabsTrigger value="data" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Dati
          </TabsTrigger>
          <TabsTrigger value="ui" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Interfaccia
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifiche
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Avanzate
          </TabsTrigger>
        </TabsList>

        {/* AI & Ollama Settings */}
        <TabsContent value="ai" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Configurazione AI
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* AI Status */}
              <Alert className={aiStatus.connected ? "border-green-500" : "border-red-500"}>
                {aiStatus.connected ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertTitle>
                  {aiStatus.connected ? "AI Connesso" : "AI Non Disponibile"}
                </AlertTitle>
                <AlertDescription>
                  {aiStatus.connected 
                    ? `Ollama connesso con ${aiStatus.models.length} modelli disponibili`
                    : aiStatus.error || "Impossibile connettersi a Ollama"
                  }
                </AlertDescription>
              </Alert>

              {/* Enable AI */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="enableAI" className="text-base font-medium">
                    Abilita AI Assistant
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Attiva l'assistente AI per insights intelligenti sulla flotta
                  </p>
                </div>
                <Switch
                  id="enableAI"
                  checked={settings.enableAI}
                  onCheckedChange={(checked) => updateSetting('enableAI', checked)}
                />
              </div>

              <Separator />

              {/* Ollama Configuration */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Server className="h-5 w-5" />
                  Configurazione Ollama
                </h3>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="ollamaEndpoint">Endpoint Ollama</Label>
                    <Input
                      id="ollamaEndpoint"
                      value={settings.ollamaEndpoint}
                      onChange={(e) => updateSetting('ollamaEndpoint', e.target.value)}
                      placeholder="http://localhost:11434"
                    />
                    <p className="text-xs text-muted-foreground">
                      URL dell'istanza Ollama
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="ollamaModel">Modello AI</Label>
                    <Select
                      value={settings.ollamaModel}
                      onValueChange={(value) => updateSetting('ollamaModel', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {aiChatService.getAvailableModels().map(model => (
                          <SelectItem key={model} value={model}>
                            {model}
                            {model === aiChatService.getModelRecommendation() && (
                              <Badge className="ml-2 text-xs">Consigliato</Badge>
                            )}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      Modello da utilizzare per l'AI
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button 
                    onClick={testAIConnection} 
                    disabled={isTestingConnection}
                    variant="outline"
                  >
                    {isTestingConnection ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Wifi className="h-4 w-4 mr-2" />
                    )}
                    Testa Connessione
                  </Button>
                  
                  {aiStatus.connected && (
                    <Badge variant="secondary">
                      {aiStatus.models.length} modelli disponibili
                    </Badge>
                  )}
                </div>

                {/* Available Models */}
                {aiStatus.models.length > 0 && (
                  <div className="space-y-2">
                    <Label>Modelli Disponibili</Label>
                    <div className="flex flex-wrap gap-2">
                      {aiStatus.models.map(model => (
                        <Badge 
                          key={model} 
                          variant={model === settings.ollamaModel ? "default" : "outline"}
                        >
                          {model}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <Separator />

              {/* AI Setup Instructions */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Installazione Ollama</h3>
                <div className="space-y-3">
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertTitle>Come installare Ollama</AlertTitle>
                    <AlertDescription>
                      <ol className="list-decimal list-inside space-y-1 mt-2">
                        <li>Visita <a href="https://ollama.ai" className="text-blue-600 underline" target="_blank">https://ollama.ai</a> e scarica Ollama</li>
                        <li>Installa Ollama seguendo le istruzioni per il tuo sistema operativo</li>
                        <li>Apri un terminale ed esegui: <code className="bg-muted px-1 rounded">ollama run llama2</code></li>
                        <li>Attendi il download del modello (può richiedere alcuni minuti)</li>
                        <li>Torna qui e testa la connessione</li>
                      </ol>
                    </AlertDescription>
                  </Alert>

                  <Alert>
                    <Zap className="h-4 w-4" />
                    <AlertTitle>Modelli Consigliati per Fleet Management</AlertTitle>
                    <AlertDescription>
                      <ul className="list-disc list-inside space-y-1 mt-2">
                        <li><strong>mistral:</strong> Ottimo per analisi business e raccomandazioni</li>
                        <li><strong>llama2:</strong> Modello generale, buon punto di partenza</li>
                        <li><strong>codellama:</strong> Eccellente per analisi dati complesse</li>
                        <li><strong>neural-chat:</strong> Ottimizzato per conversazioni</li>
                      </ul>
                    </AlertDescription>
                  </Alert>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Data Settings */}
        <TabsContent value="data" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Impostazioni Dati</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="refreshInterval">Intervallo Aggiornamento (minuti)</Label>
                  <Input
                    id="refreshInterval"
                    type="number"
                    value={settings.dataRefreshInterval}
                    onChange={(e) => updateSetting('dataRefreshInterval', parseInt(e.target.value))}
                    min="1"
                    max="1440"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currency">Valuta Predefinita</Label>
                  <Select
                    value={settings.defaultCurrency}
                    onValueChange={(value) => updateSetting('defaultCurrency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="EUR">Euro (€)</SelectItem>
                      <SelectItem value="USD">Dollaro USA ($)</SelectItem>
                      <SelectItem value="GBP">Sterlina (£)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dateFormat">Formato Data</Label>
                  <Select
                    value={settings.defaultDateFormat}
                    onValueChange={(value) => updateSetting('defaultDateFormat', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                      <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                      <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Soglie di Allerta</h3>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <Label htmlFor="contractExpiration">Scadenza Contratti (giorni)</Label>
                    <Input
                      id="contractExpiration"
                      type="number"
                      value={settings.thresholds.contractExpirationWarningDays}
                      onChange={(e) => updateThreshold('contractExpirationWarningDays', parseInt(e.target.value))}
                      min="1"
                      max="365"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="highCost">Costo Alto (€)</Label>
                    <Input
                      id="highCost"
                      type="number"
                      value={settings.thresholds.highCostAlert}
                      onChange={(e) => updateThreshold('highCostAlert', parseInt(e.target.value))}
                      min="0"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lowUtilization">Utilizzo Basso (%)</Label>
                    <Input
                      id="lowUtilization"
                      type="number"
                      value={settings.thresholds.lowUtilizationAlert}
                      onChange={(e) => updateThreshold('lowUtilizationAlert', parseInt(e.target.value))}
                      min="0"
                      max="100"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* UI Settings */}
        <TabsContent value="ui" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Preferenze Interfaccia</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="theme">Tema</Label>
                  <Select
                    value={preferences.theme}
                    onValueChange={(value) => updatePreference('theme', value as 'light' | 'dark' | 'system')}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Chiaro</SelectItem>
                      <SelectItem value="dark">Scuro</SelectItem>
                      <SelectItem value="system">Sistema</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="language">Lingua</Label>
                  <Select
                    value={preferences.language}
                    onValueChange={(value) => updatePreference('language', value as 'en' | 'it')}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="it">Italiano</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dashboardLayout">Layout Dashboard</Label>
                  <Select
                    value={preferences.dashboardLayout}
                    onValueChange={(value) => updatePreference('dashboardLayout', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="default">Predefinito</SelectItem>
                      <SelectItem value="compact">Compatto</SelectItem>
                      <SelectItem value="expanded">Espanso</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Impostazioni Notifiche</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="enableNotifications" className="text-base font-medium">
                    Abilita Notifiche
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Ricevi notifiche per eventi importanti della flotta
                  </p>
                </div>
                <Switch
                  id="enableNotifications"
                  checked={settings.enableNotifications}
                  onCheckedChange={(checked) => updateSetting('enableNotifications', checked)}
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Tipi di Notifica</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Scadenze Contratti</Label>
                      <p className="text-sm text-muted-foreground">
                        Avvisi per contratti in scadenza
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Costi Elevati</Label>
                      <p className="text-sm text-muted-foreground">
                        Notifiche per veicoli con costi sopra soglia
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Basso Utilizzo</Label>
                      <p className="text-sm text-muted-foreground">
                        Avvisi per veicoli sottoutilizzati
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Advanced Settings */}
        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Impostazioni Avanzate</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Backup e Ripristino</h3>
                <div className="flex items-center space-x-2">
                  <Button onClick={exportSettings} variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Esporta Impostazioni
                  </Button>
                  
                  <div>
                    <input
                      type="file"
                      accept=".json"
                      onChange={importSettings}
                      className="hidden"
                      id="import-settings"
                    />
                    <Button variant="outline" asChild>
                      <label htmlFor="import-settings" className="cursor-pointer">
                        <Upload className="h-4 w-4 mr-2" />
                        Importa Impostazioni
                      </label>
                    </Button>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Reset</h3>
                <div className="flex items-center space-x-2">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="destructive">
                        Ripristina Predefinite
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Conferma Ripristino</DialogTitle>
                        <DialogDescription>
                          Sei sicuro di voler ripristinare tutte le impostazioni ai valori predefiniti?
                          Questa azione non può essere annullata.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="flex justify-end space-x-2">
                        <Button variant="outline">Annulla</Button>
                        <Button variant="destructive" onClick={resetToDefaults}>
                          Ripristina
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Informazioni Sistema</h3>
                <div className="grid gap-2 text-sm">
                  <div className="flex justify-between">
                    <span>Versione:</span>
                    <span>1.0.0</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Ultimo aggiornamento dati:</span>
                    <span>{new Date().toLocaleString('it-IT')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Browser:</span>
                    <span>{navigator.userAgent.split(' ')[0]}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}