import React, { useState, useEffect, useMemo } from 'react';
import { Breadcrumbs } from "@/components/layout/Breadcrumbs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table, TableBody, TableCell, TableHead, TableHeader, TableRow
} from "@/components/ui/table";
import {
  Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel,
  DropdownMenuSeparator, DropdownMenuTrigger, DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import {
  Search, Download, Filter, MoreHorizontal, Eye, Edit, Trash2,
  SortAsc, SortDesc, RefreshCw, Settings, FileText, Database,
  ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight
} from 'lucide-react';
import enhancedFleetDataService from '@/services/enhancedFleetDataService';
import { FleetVehicle, AdvancedFilterOptions } from '@/types/fleet';

interface Column {
  key: keyof FleetVehicle;
  label: string;
  sortable: boolean;
  filterable: boolean;
  visible: boolean;
  width?: string;
  format?: (value: any) => string;
}

export default function EnhancedData() {
  const [vehicles, setVehicles] = useState<FleetVehicle[]>([]);
  const [filteredVehicles, setFilteredVehicles] = useState<FleetVehicle[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<AdvancedFilterOptions>({});
  const [quickFilters, setQuickFilters] = useState({
    activeOnly: false,
    hasInsurance: false,
    expiringContracts: false,
  });

  // Table states
  const [sortConfig, setSortConfig] = useState<{key: keyof FleetVehicle; direction: 'asc' | 'desc'} | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  
  // UI states
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showColumnSettings, setShowColumnSettings] = useState(false);

  // Column configuration
  const [columns, setColumns] = useState<Column[]>([
    { key: 'targa', label: 'Targa', sortable: true, filterable: true, visible: true, width: '120px' },
    { key: 'modello', label: 'Modello', sortable: true, filterable: true, visible: true, width: '200px' },
    { key: 'categoria', label: 'Categoria', sortable: true, filterable: true, visible: true, width: '150px' },
    { key: 'gruppo', label: 'Gruppo', sortable: true, filterable: true, visible: true, width: '120px' },
    { key: 'stato', label: 'Stato', sortable: true, filterable: true, visible: true, width: '100px' },
    { key: 'proprieta', label: 'Proprietà', sortable: true, filterable: true, visible: true, width: '180px' },
    { key: 'parcoSede', label: 'Sede', sortable: true, filterable: true, visible: true, width: '150px' },
    { 
      key: 'canoneImponibile', 
      label: 'Canone €', 
      sortable: true, 
      filterable: true, 
      visible: true, 
      width: '120px',
      format: (value: number) => new Intl.NumberFormat('it-IT', { 
        style: 'currency', 
        currency: 'EUR' 
      }).format(value)
    },
    { 
      key: 'dataFine', 
      label: 'Scadenza', 
      sortable: true, 
      filterable: true, 
      visible: true, 
      width: '120px',
      format: (value: Date | null) => value ? value.toLocaleDateString('it-IT') : '-'
    },
    { key: 'acquisizione', label: 'Acquisizione', sortable: true, filterable: true, visible: false, width: '120px' },
    { key: 'tipologia', label: 'Tipologia', sortable: true, filterable: true, visible: false, width: '120px' },
    { key: 'trasmissione', label: 'Trasmissione', sortable: true, filterable: true, visible: false, width: '130px' },
    { 
      key: 'durataInMesi', 
      label: 'Durata (mesi)', 
      sortable: true, 
      filterable: true, 
      visible: false, 
      width: '120px',
      format: (value: number) => value > 0 ? value.toString() : '-'
    },
    { 
      key: 'kmInclusi', 
      label: 'KM Inclusi', 
      sortable: true, 
      filterable: true, 
      visible: false, 
      width: '120px',
      format: (value: number) => value > 0 ? new Intl.NumberFormat('it-IT').format(value) : '-'
    },
  ]);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [vehicles, searchTerm, filters, quickFilters]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load data if not already loaded
      if (!enhancedFleetDataService.getVehicles().length) {
        const response = await fetch('/data/RegistroFLOTTA_Master.csv');
        const csvContent = await response.text();
        await enhancedFleetDataService.loadData(csvContent);
      }

      const loadedVehicles = enhancedFleetDataService.getVehicles();
      setVehicles(loadedVehicles);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
      console.error('Error loading data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...vehicles];

    // Search term filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(vehicle => 
        vehicle.targa.toLowerCase().includes(searchLower) ||
        vehicle.modello.toLowerCase().includes(searchLower) ||
        vehicle.categoria.toLowerCase().includes(searchLower) ||
        vehicle.proprieta.toLowerCase().includes(searchLower) ||
        vehicle.parcoSede.toLowerCase().includes(searchLower)
      );
    }

    // Quick filters
    if (quickFilters.activeOnly) {
      filtered = filtered.filter(v => v.stato === 'Attivo');
    }
    if (quickFilters.hasInsurance) {
      filtered = filtered.filter(v => v.coperturaAssicurativa > 0);
    }
    if (quickFilters.expiringContracts) {
      const today = new Date();
      const thirtyDaysFromNow = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
      filtered = filtered.filter(v => 
        v.dataFine && v.dataFine >= today && v.dataFine <= thirtyDaysFromNow
      );
    }

    // Advanced filters
    filtered = enhancedFleetDataService.getVehicles(filters);

    setFilteredVehicles(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Sorting logic
  const sortedVehicles = useMemo(() => {
    let sortableVehicles = [...filteredVehicles];
    
    if (sortConfig !== null) {
      sortableVehicles.sort((a, b) => {
        const aValue = a[sortConfig.key];
        const bValue = b[sortConfig.key];
        
        if (aValue === null || aValue === undefined) return 1;
        if (bValue === null || bValue === undefined) return -1;
        
        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }
    
    return sortableVehicles;
  }, [filteredVehicles, sortConfig]);

  // Pagination logic
  const paginatedVehicles = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return sortedVehicles.slice(startIndex, startIndex + pageSize);
  }, [sortedVehicles, currentPage, pageSize]);

  const totalPages = Math.ceil(sortedVehicles.length / pageSize);

  const handleSort = (key: keyof FleetVehicle) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handleRowSelect = (vehicleId: string) => {
    const newSelected = new Set(selectedRows);
    if (newSelected.has(vehicleId)) {
      newSelected.delete(vehicleId);
    } else {
      newSelected.add(vehicleId);
    }
    setSelectedRows(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedRows.size === paginatedVehicles.length) {
      setSelectedRows(new Set());
    } else {
      setSelectedRows(new Set(paginatedVehicles.map(v => v.id)));
    }
  };

  const exportData = () => {
    const dataToExport = selectedRows.size > 0 
      ? sortedVehicles.filter(v => selectedRows.has(v.id))
      : sortedVehicles;
    
    const csv = enhancedFleetDataService.exportToCSV(filters);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `fleet_data_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const visibleColumns = columns.filter(col => col.visible);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Breadcrumbs />
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Caricamento dati...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Breadcrumbs />
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <Database className="h-8 w-8 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Errore nel caricamento</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={loadData}>Riprova</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Breadcrumbs />
      
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">📊 Gestione Dati</h1>
          <p className="text-muted-foreground">
            Visualizzazione, filtraggio e esportazione avanzata dei dati della flotta
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">
            {new Intl.NumberFormat('it-IT').format(filteredVehicles.length)} di {new Intl.NumberFormat('it-IT').format(vehicles.length)} record
          </Badge>
          {selectedRows.size > 0 && (
            <Badge variant="outline">
              {selectedRows.size} selezionati
            </Badge>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Filtri e Ricerca</CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtri Avanzati
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowColumnSettings(!showColumnSettings)}
              >
                <Settings className="h-4 w-4 mr-2" />
                Colonne
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Bar */}
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Cerca per targa, modello, categoria, proprietà o sede..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button onClick={loadData} variant="outline" size="icon">
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>

          {/* Quick Filters */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="activeOnly"
                checked={quickFilters.activeOnly}
                onCheckedChange={(checked) => 
                  setQuickFilters(prev => ({ ...prev, activeOnly: checked as boolean }))
                }
              />
              <Label htmlFor="activeOnly" className="text-sm">Solo Attivi</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="hasInsurance"
                checked={quickFilters.hasInsurance}
                onCheckedChange={(checked) => 
                  setQuickFilters(prev => ({ ...prev, hasInsurance: checked as boolean }))
                }
              />
              <Label htmlFor="hasInsurance" className="text-sm">Con Assicurazione</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="expiringContracts"
                checked={quickFilters.expiringContracts}
                onCheckedChange={(checked) => 
                  setQuickFilters(prev => ({ ...prev, expiringContracts: checked as boolean }))
                }
              />
              <Label htmlFor="expiringContracts" className="text-sm">In Scadenza (30gg)</Label>
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
              <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-4">
                <div>
                  <Label htmlFor="stato">Stato</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Seleziona stato" />
                    </SelectTrigger>
                    <SelectContent>
                      {enhancedFleetDataService.getUniqueValues('stato').map(stato => (
                        <SelectItem key={stato} value={stato}>{stato}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="categoria">Categoria</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Seleziona categoria" />
                    </SelectTrigger>
                    <SelectContent>
                      {enhancedFleetDataService.getUniqueValues('categoria').map(categoria => (
                        <SelectItem key={categoria} value={categoria}>{categoria}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="proprieta">Proprietà</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Seleziona proprietà" />
                    </SelectTrigger>
                    <SelectContent>
                      {enhancedFleetDataService.getUniqueValues('proprieta').map(proprieta => (
                        <SelectItem key={proprieta} value={proprieta}>{proprieta}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="acquisizione">Acquisizione</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Seleziona tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      {enhancedFleetDataService.getUniqueValues('acquisizione').map(acq => (
                        <SelectItem key={acq} value={acq}>{acq}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {/* Column Settings */}
          {showColumnSettings && (
            <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
              <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-4">
                {columns.map(column => (
                  <div key={column.key} className="flex items-center space-x-2">
                    <Checkbox
                      id={column.key}
                      checked={column.visible}
                      onCheckedChange={(checked) => {
                        setColumns(prev => prev.map(col => 
                          col.key === column.key ? { ...col, visible: checked as boolean } : col
                        ));
                      }}
                    />
                    <Label htmlFor={column.key} className="text-sm">{column.label}</Label>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Dati Flotta</CardTitle>
            <div className="flex items-center space-x-2">
              <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(parseInt(value))}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                  <SelectItem value="250">250</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={exportData} size="sm">
                <Download className="h-4 w-4 mr-2" />
                Esporta
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedRows.size === paginatedVehicles.length && paginatedVehicles.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  {visibleColumns.map(column => (
                    <TableHead 
                      key={column.key} 
                      className={`${column.width ? `w-[${column.width}]` : ''} ${column.sortable ? 'cursor-pointer select-none' : ''}`}
                      onClick={column.sortable ? () => handleSort(column.key) : undefined}
                    >
                      <div className="flex items-center space-x-1">
                        <span>{column.label}</span>
                        {column.sortable && sortConfig?.key === column.key && (
                          sortConfig.direction === 'asc' ? 
                            <SortAsc className="h-4 w-4" /> : 
                            <SortDesc className="h-4 w-4" />
                        )}
                      </div>
                    </TableHead>
                  ))}
                  <TableHead className="w-12">Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedVehicles.map((vehicle) => (
                  <TableRow key={vehicle.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedRows.has(vehicle.id)}
                        onCheckedChange={() => handleRowSelect(vehicle.id)}
                      />
                    </TableCell>
                    {visibleColumns.map(column => (
                      <TableCell key={column.key}>
                        {column.format 
                          ? column.format(vehicle[column.key])
                          : String(vehicle[column.key] || '-')
                        }
                      </TableCell>
                    ))}
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Azioni</DropdownMenuLabel>
                          <DropdownMenuItem>
                            <Eye className="mr-2 h-4 w-4" />
                            Visualizza
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Modifica
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Elimina
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between px-2 py-4">
            <div className="text-sm text-muted-foreground">
              Visualizzazione {((currentPage - 1) * pageSize) + 1} - {Math.min(currentPage * pageSize, sortedVehicles.length)} di {sortedVehicles.length} risultati
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm font-medium">
                Pagina {currentPage} di {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}