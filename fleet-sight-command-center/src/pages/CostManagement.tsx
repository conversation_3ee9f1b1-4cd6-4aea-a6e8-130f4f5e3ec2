import React from 'react';
import { Breadcrumbs } from "@/components/layout/Breadcrumbs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DollarSign, TrendingUp, TrendingDown, AlertTriangle } from 'lucide-react';

export default function CostManagement() {
  return (
    <div className="space-y-6">
      <Breadcrumbs />
      
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">💰 Cost Management</h1>
          <p className="text-muted-foreground">
            Gestione e analisi dei costi della flotta
          </p>
        </div>

        {/* Cost Analysis Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Costi Totali Mensili</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">€847,265</div>
              <p className="text-xs text-muted-foreground">
                +2.5% da mese scorso
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Risparmi Ottenuti</CardTitle>
              <TrendingDown className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">€23,847</div>
              <p className="text-xs text-muted-foreground">
                Da consegne anticipate
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Trend Costi</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">+3.2%</div>
              <p className="text-xs text-muted-foreground">
                Incremento ultimo trimestre
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Alert Costi</CardTitle>
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">12</div>
              <p className="text-xs text-muted-foreground">
                Veicoli sopra budget
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Cost Analysis Content */}
        <Card>
          <CardHeader>
            <CardTitle>Analisi Costi Dettagliata</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Questa sezione sarà implementata con grafici dettagliati di analisi dei costi,
              breakdown per categoria, tendenze storiche e proiezioni future.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}