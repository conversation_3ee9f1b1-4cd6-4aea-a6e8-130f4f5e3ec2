import { useState } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { SidebarProvider } from "@/components/ui/sidebar";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { AppHeader } from "@/components/layout/AppHeader";
import { FilterPanel } from "@/components/layout/FilterPanel";
import Overview from "./pages/Overview";
import FleetAnalysis from "./pages/FleetAnalysis";
import EnhancedFleetAnalysis from "./pages/EnhancedFleetAnalysis";
import CostManagement from "./pages/CostManagement";
import Data from "./pages/Data";
import EnhancedData from "./pages/EnhancedData";
import Settings from "./pages/Settings";
import EnhancedSettings from "./pages/EnhancedSettings";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

function AppLayout({ children }: { children: React.ReactNode }) {
  const [showFilterPanel, setShowFilterPanel] = useState(false);

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col min-w-0">
          <AppHeader 
            onFilterToggle={() => setShowFilterPanel(!showFilterPanel)}
            showFilterPanel={showFilterPanel}
          />
          <div className="flex-1 flex relative">
            <main className="flex-1 p-6 overflow-auto">
              {children}
            </main>
            <FilterPanel 
              isOpen={showFilterPanel} 
              onClose={() => setShowFilterPanel(false)} 
            />
          </div>
        </div>
      </div>
    </SidebarProvider>
  );
}

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <AppLayout>
          <Routes>
            <Route path="/" element={<Overview />} />
            <Route path="/fleet" element={<EnhancedFleetAnalysis />} />
            <Route path="/fleet-basic" element={<FleetAnalysis />} />
            <Route path="/costs" element={<CostManagement />} />
            <Route path="/data" element={<EnhancedData />} />
            <Route path="/data-basic" element={<Data />} />
            <Route path="/settings" element={<EnhancedSettings />} />
            <Route path="/settings-basic" element={<Settings />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </AppLayout>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
