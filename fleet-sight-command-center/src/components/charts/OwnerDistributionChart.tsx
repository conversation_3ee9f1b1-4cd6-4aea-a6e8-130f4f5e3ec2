import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContaine<PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface OwnerData {
  proprieta: string;
  vehicleCount: number;
  totalCost: number;
  percentage: number;
}

interface OwnerDistributionChartProps {
  data: OwnerData[];
  title?: string;
  className?: string;
}

const CHART_COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff7f',
  '#dc143c', '#00bfff', '#ff69b4', '#32cd32', '#ff4500'
];

export function OwnerDistributionChart({ data, title = "Fleet Distribution by Owner", className }: OwnerDistributionChartProps) {
  // Show top 8 owners, group others
  const topOwners = data.slice(0, 8);
  const others = data.slice(8);
  
  let chartData = [...topOwners];
  
  if (others.length > 0) {
    const othersTotal = others.reduce((sum, owner) => ({
      proprieta: 'Altri',
      vehicleCount: sum.vehicleCount + owner.vehicleCount,
      totalCost: sum.totalCost + owner.totalCost,
      percentage: sum.percentage + owner.percentage
    }), { proprieta: 'Altri', vehicleCount: 0, totalCost: 0, percentage: 0 });
    
    chartData.push(othersTotal);
  }

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('it-IT').format(value);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ proprieta, percentage }) => percentage > 5 ? `${proprieta} (${percentage.toFixed(1)}%)` : ''}
              outerRadius={80}
              fill="#8884d8"
              dataKey="vehicleCount"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
              ))}
            </Pie>
            <Tooltip 
              formatter={(value: any, name: any, props: any) => [
                `${formatNumber(value)} veicoli`,
                props.payload.proprieta
              ]}
              labelFormatter={(label: any, payload: any) => {
                if (payload && payload[0]) {
                  return `${payload[0].payload.proprieta} - ${formatCurrency(payload[0].payload.totalCost)}`;
                }
                return label;
              }}
            />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}