import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, AlertTriangle, CheckCircle, Wrench } from "lucide-react";
import { TimelineEvent } from "@/services/mockDataService";
import { useDashboardStore } from "@/stores/dashboardStore";
import { format, isToday, isTomorrow, isYesterday } from "date-fns";

interface TimelineChartProps {
  data: TimelineEvent[];
  title?: string;
  className?: string;
}

export function TimelineChart({ data, title = "Maintenance Timeline", className }: TimelineChartProps) {
  const { setFilters, activeFilters } = useDashboardStore();

  // Validate data to prevent crashes
  if (!data || !Array.isArray(data)) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg">{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">No timeline data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusColor = (status: TimelineEvent['status']) => {
    switch (status) {
      case 'completed': return 'bg-success text-success-foreground';
      case 'in-progress': return 'bg-warning text-warning-foreground';
      case 'overdue': return 'bg-destructive text-destructive-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getPriorityIcon = (priority: TimelineEvent['priority']) => {
    switch (priority) {
      case 'critical': return <AlertTriangle className="w-3 h-3 text-destructive" />;
      case 'high': return <AlertTriangle className="w-3 h-3 text-warning" />;
      case 'medium': return <Clock className="w-3 h-3 text-muted-foreground" />;
      default: return <CheckCircle className="w-3 h-3 text-muted-foreground" />;
    }
  };

  const getTypeIcon = (type: TimelineEvent['type']) => {
    switch (type) {
      case 'maintenance': return <Wrench className="w-4 h-4" />;
      case 'service': return <Wrench className="w-4 h-4" />;
      case 'inspection': return <CheckCircle className="w-4 h-4" />;
      default: return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const formatDate = (date: Date) => {
    if (isToday(date)) return 'Today';
    if (isTomorrow(date)) return 'Tomorrow';
    if (isYesterday(date)) return 'Yesterday';
    return format(date, 'MMM d');
  };

  const handleEventClick = (event: TimelineEvent) => {
    setFilters({
      selectedVehicle: event.vehicleId,
      selectedEventType: event.type
    });
  };

  const filteredData = data.filter(event => {
    if (activeFilters.selectedEventType && event.type !== activeFilters.selectedEventType) {
      return false;
    }
    if (activeFilters.selectedVehicle && event.vehicleId !== activeFilters.selectedVehicle) {
      return false;
    }
    return true;
  });

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{title}</CardTitle>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setFilters({ selectedEventType: 'maintenance' })}
              className={activeFilters.selectedEventType === 'maintenance' ? 'bg-primary text-primary-foreground' : ''}
            >
              Maintenance
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setFilters({ selectedEventType: 'service' })}
              className={activeFilters.selectedEventType === 'service' ? 'bg-primary text-primary-foreground' : ''}
            >
              Service
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="max-h-96 overflow-y-auto space-y-3">
          {filteredData.slice(0, 20).map((event) => (
            <div
              key={event.id}
              onClick={() => handleEventClick(event)}
              className="flex items-center gap-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer transition-colors"
            >
              <div className="flex-shrink-0">
                {getTypeIcon(event.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-sm truncate">
                    {event.title}
                  </span>
                  {getPriorityIcon(event.priority)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {event.vehicleName}
                </div>
              </div>
              
              <div className="flex-shrink-0 text-right">
                <div className="text-sm font-medium mb-1">
                  {formatDate(event.startDate)}
                </div>
                <Badge 
                  className={`text-xs ${getStatusColor(event.status)}`}
                  variant="secondary"
                >
                  {event.status}
                </Badge>
              </div>
              
              {event.cost && (
                <div className="flex-shrink-0 text-sm font-medium text-muted-foreground">
                  ${event.cost.toLocaleString()}
                </div>
              )}
            </div>
          ))}
        </div>
        
        <div className="flex items-center justify-between pt-3 border-t">
          <div className="text-sm text-muted-foreground">
            Showing {Math.min(filteredData.length, 20)} of {filteredData.length} events
          </div>
          <Button variant="outline" size="sm">
            <Calendar className="w-4 h-4 mr-2" />
            View Calendar
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}