import React from 'react';
import { Composed<PERSON>hart, Bar, Line, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, <PERSON>lt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface SavingsData {
  category: string;
  vehicleCount: number;
  totalSavings: number;
  averageSavings: number;
  daysSaved: number;
}

interface SavingsAnalysisChartProps {
  data: SavingsData[];
  title?: string;
  className?: string;
}

export function SavingsAnalysisChart({ data, title = "Savings Analysis by Category", className }: SavingsAnalysisChartProps) {
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('it-IT').format(value);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <ComposedChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="category" 
              angle={-45} 
              textAnchor="end" 
              height={100}
              interval={0}
            />
            <YAxis yAxisId="left" />
            <YAxis yAxisId="right" orientation="right" />
            <Tooltip 
              formatter={(value: any, name: any) => [
                name === 'vehicleCount' ? formatNumber(value) : 
                name === 'daysSaved' ? `${formatNumber(value)} giorni` :
                formatCurrency(value),
                name === 'vehicleCount' ? 'Veicoli' : 
                name === 'daysSaved' ? 'Giorni Risparmiati' :
                name === 'totalSavings' ? 'Risparmi Totali' : 'Risparmio Medio'
              ]}
            />
            <Legend />
            <Bar yAxisId="left" dataKey="vehicleCount" fill="#8884d8" name="Veicoli" />
            <Bar yAxisId="right" dataKey="totalSavings" fill="#82ca9d" name="Risparmi Totali" />
            <Line yAxisId="right" type="monotone" dataKey="averageSavings" stroke="#ff7300" name="Risparmio Medio" />
          </ComposedChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}