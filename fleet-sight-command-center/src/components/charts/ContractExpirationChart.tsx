import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ExpirationData {
  month: string;
  expiringContracts: number;
  costReduction: number;
}

interface ContractExpirationChartProps {
  data: ExpirationData[];
  title?: string;
  className?: string;
}

export function ContractExpirationChart({ data, title = "Contract Expiration Timeline", className }: ContractExpirationChartProps) {
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('it-IT').format(value);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip 
              formatter={(value: any, name: any) => [
                name === 'expiringContracts' ? formatNumber(value) : formatCurrency(value),
                name === 'expiringContracts' ? 'Contratti in Scadenza' : 'Riduzione Costi'
              ]}
            />
            <Line 
              type="monotone" 
              dataKey="expiringContracts" 
              stroke="#8884d8" 
              strokeWidth={2}
              name="Contratti in Scadenza"
            />
            <Line 
              type="monotone" 
              dataKey="costReduction" 
              stroke="#82ca9d" 
              strokeWidth={2}
              name="Riduzione Costi"
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}