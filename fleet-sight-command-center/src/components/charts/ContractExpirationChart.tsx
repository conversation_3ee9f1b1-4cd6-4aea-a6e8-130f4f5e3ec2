import React from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ExpirationData {
  month: string;
  expiring: number;
  total: number;
}

interface ContractExpirationChartProps {
  data: ExpirationData[];
  title?: string;
  className?: string;
}

export function ContractExpirationChart({ data, title = "Contract Expiration Timeline", className }: ContractExpirationChartProps) {
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('it-IT').format(value);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip
              formatter={(value: any, name: any) => [
                formatNumber(value),
                name === 'expiring' ? 'Contratti in Scadenza' : 'Totale Contratti'
              ]}
            />
            <Line
              type="monotone"
              dataKey="expiring"
              stroke="#8884d8"
              strokeWidth={2}
              name="Contratti in Scadenza"
            />
            <Line
              type="monotone"
              dataKey="total"
              stroke="#82ca9d"
              strokeWidth={2}
              name="Totale Contratti"
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}