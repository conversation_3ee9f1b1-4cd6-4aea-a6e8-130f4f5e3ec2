import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianG<PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface CostAnalysisData {
  categoria: string;
  count: number;
  totalCost: number;
  averageCost: number;
}

interface CostAnalysisChartProps {
  data: CostAnalysisData[];
  title?: string;
  className?: string;
}

export function CostAnalysisChart({ data, title = "Cost Analysis by Category", className }: CostAnalysisChartProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('it-IT').format(value);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="categoria" 
              angle={-45} 
              textAnchor="end" 
              height={100}
              interval={0}
            />
            <YAxis />
            <Tooltip 
              formatter={(value: any, name: any) => [
                name === 'count' ? formatNumber(value) : formatCurrency(value),
                name === 'count' ? 'Veicoli' : name === 'totalCost' ? 'Costo Totale' : 'Costo Medio'
              ]}
            />
            <Legend />
            <Bar dataKey="count" fill="#8884d8" name="Numero Veicoli" />
            <Bar dataKey="averageCost" fill="#82ca9d" name="Costo Medio" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}