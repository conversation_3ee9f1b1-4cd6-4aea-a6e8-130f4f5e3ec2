import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContaine<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface FleetCompositionData {
  categoria: string;
  count: number;
  percentage: number;
  color: string;
}

interface FleetCompositionChartProps {
  data: FleetCompositionData[];
  title?: string;
  className?: string;
}

const CHART_COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff7f',
  '#dc143c', '#00bfff', '#ff69b4', '#32cd32', '#ff4500'
];

export function FleetCompositionChart({ data, title = "Fleet Composition", className }: FleetCompositionChartProps) {
  // Add colors to data
  const dataWithColors = data.map((item, index) => ({
    ...item,
    color: CHART_COLORS[index % CHART_COLORS.length]
  }));

  const formatN<PERSON>ber = (value: number) => {
    return new Intl.NumberFormat('it-IT').format(value);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={dataWithColors}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ categoria, percentage }) => `${categoria} (${percentage.toFixed(1)}%)`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="count"
            >
              {dataWithColors.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip 
              formatter={(value: any, name: any, props: any) => [
                `${formatNumber(value)} veicoli`,
                props.payload.categoria
              ]}
            />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}