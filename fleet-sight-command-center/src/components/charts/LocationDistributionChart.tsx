import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface LocationData {
  location: string;
  vehicleCount: number;
  totalCost: number;
  utilizationRate: number;
}

interface LocationDistributionChartProps {
  data: LocationData[];
  title?: string;
  className?: string;
}

export function LocationDistributionChart({ data, title = "Vehicle Distribution by Location", className }: LocationDistributionChartProps) {
  // Show top 10 locations
  const topLocations = data.slice(0, 10);

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('it-IT').format(value);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart 
            data={topLocations} 
            layout="horizontal"
            margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis 
              dataKey="location" 
              type="category" 
              width={80}
              interval={0}
            />
            <Tooltip 
              formatter={(value: any, name: any, props: any) => [
                name === 'vehicleCount' ? formatNumber(value) : 
                name === 'totalCost' ? formatCurrency(value) :
                `${value.toFixed(1)}%`,
                name === 'vehicleCount' ? 'Veicoli' : 
                name === 'totalCost' ? 'Costo Totale' : 'Utilizzo'
              ]}
              labelFormatter={(label: any) => `Sede: ${label}`}
            />
            <Bar dataKey="vehicleCount" fill="#8884d8" name="Numero Veicoli" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}