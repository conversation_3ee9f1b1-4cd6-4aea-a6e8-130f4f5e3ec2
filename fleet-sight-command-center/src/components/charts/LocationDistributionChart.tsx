import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface LocationData {
  location: string;
  count: number;
  percentage: number;
}

interface LocationDistributionChartProps {
  data: LocationData[];
  title?: string;
  className?: string;
}

export function LocationDistributionChart({ data, title = "Vehicle Distribution by Location", className }: LocationDistributionChartProps) {
  // Show top 10 locations
  const topLocations = data.slice(0, 10);

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('it-IT').format(value);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart 
            data={topLocations} 
            layout="horizontal"
            margin={{ top: 20, right: 30, left: 100, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" />
            <YAxis 
              dataKey="location" 
              type="category" 
              width={80}
              interval={0}
            />
            <Tooltip
              formatter={(value: any, name: any) => [
                name === 'count' ? formatNumber(value) : `${value.toFixed(1)}%`,
                name === 'count' ? 'Veicoli' : 'Percentuale'
              ]}
              labelFormatter={(label: any) => `Sede: ${label}`}
            />
            <Bar dataKey="count" fill="#8884d8" name="Numero Veicoli" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}