import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Cell } from "recharts";
import { WaterfallData } from "@/services/mockDataService";
import { useDashboardStore } from "@/stores/dashboardStore";

interface WaterfallChartProps {
  data: WaterfallData[];
  title?: string;
  className?: string;
}

const chartConfig = {
  positive: {
    label: "Positive",
    color: "hsl(var(--success))",
  },
  negative: {
    label: "Negative", 
    color: "hsl(var(--destructive))",
  },
  total: {
    label: "Total",
    color: "hsl(var(--primary))",
  },
};

export function WaterfallChart({ data, title = "Cost Waterfall Analysis", className }: WaterfallChartProps) {
  const { setFilters } = useDashboardStore();

  // Validate data to prevent crashes
  if (!data || !Array.isArray(data)) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg">{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">No waterfall data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getBarColor = (type: WaterfallData['type']) => {
    switch (type) {
      case 'positive': return chartConfig.positive.color;
      case 'negative': return chartConfig.negative.color;
      case 'total': return chartConfig.total.color;
      default: return chartConfig.total.color;
    }
  };

  const handleBarClick = (data: WaterfallData) => {
    setFilters({
      selectedCostCategory: data.category,
      costType: data.type
    });
  };

  // Transform data for waterfall visualization
  const chartData = data.map((item, index) => {
    const previousCumulative = index > 0 ? data[index - 1].cumulative : 0;
    
    return {
      ...item,
      displayValue: Math.abs(item.value),
      baseValue: item.type === 'total' ? 0 : 
                 item.type === 'positive' ? previousCumulative :
                 previousCumulative + item.value,
      formattedValue: `$${(Math.abs(item.value) / 1000).toFixed(0)}K`,
      formattedCumulative: `$${(item.cumulative / 1000).toFixed(0)}K`
    };
  });

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">{title}</CardTitle>
        <p className="text-sm text-muted-foreground">
          Monthly cost flow analysis showing budget impact
        </p>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
              <XAxis 
                dataKey="category" 
                angle={-45}
                textAnchor="end"
                height={80}
                fontSize={12}
                stroke="hsl(var(--muted-foreground))"
              />
              <YAxis 
                tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
                fontSize={12}
                stroke="hsl(var(--muted-foreground))"
              />
              <ChartTooltip 
                content={
                  <ChartTooltipContent 
                    formatter={(value, name, props) => [
                      `$${(props.payload.value / 1000).toFixed(0)}K`,
                      props.payload.type === 'positive' ? 'Gain' : 
                      props.payload.type === 'negative' ? 'Cost' : 'Total'
                    ]}
                    labelFormatter={(label) => `${label}`}
                  />
                }
              />
              <Bar 
                dataKey="displayValue"
                onClick={handleBarClick}
                className="cursor-pointer"
              >
                {chartData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={getBarColor(entry.type)}
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
        
        <div className="mt-4 flex flex-wrap gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-sm bg-success"></div>
            <span className="text-sm text-muted-foreground">Positive Impact</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-sm bg-destructive"></div>
            <span className="text-sm text-muted-foreground">Cost Impact</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-sm bg-primary"></div>
            <span className="text-sm text-muted-foreground">Total Position</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}