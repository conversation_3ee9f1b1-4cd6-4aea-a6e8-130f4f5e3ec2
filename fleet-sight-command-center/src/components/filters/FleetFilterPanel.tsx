import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  CalendarIcon, 
  FilterIcon, 
  XIcon, 
  RefreshCwIcon,
  ZapIcon,
  TrendingUpIcon,
  ClockIcon,
  DollarSignIcon
} from 'lucide-react';
import { format } from 'date-fns';
import { it } from 'date-fns/locale';
import { FilterOptions, DateFilterOptions } from '@/services/fleetDataService';

interface FleetFilterPanelProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  availableOptions: {
    stato: string[];
    categoria: string[];
    gruppo: string[];
    proprieta: string[];
    parcoSede: string[];
    assicurazione: string[];
  };
  isOpen: boolean;
  onToggle: () => void;
}

export function FleetFilterPanel({
  filters,
  onFiltersChange,
  availableOptions,
  isOpen,
  onToggle,
}: FleetFilterPanelProps) {
  const [localFilters, setLocalFilters] = useState<FilterOptions>(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const applyFilters = () => {
    onFiltersChange(localFilters);
  };

  const clearAllFilters = () => {
    const emptyFilters: FilterOptions = {};
    setLocalFilters(emptyFilters);
    onFiltersChange(emptyFilters);
  };

  const applyPreset = (preset: string) => {
    const today = new Date();
    let presetFilters: FilterOptions = {};

    switch (preset) {
      case 'last_7_days':
        const sevenDaysAgo = new Date(today);
        sevenDaysAgo.setDate(today.getDate() - 7);
        presetFilters = {
          dateFilter: {
            dateField: 'DATA_FINE',
            startDate: sevenDaysAgo,
            endDate: today,
          },
        };
        break;
      case 'this_month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        presetFilters = {
          dateFilter: {
            dateField: 'DATA_FINE',
            startDate: monthStart,
            endDate: today,
          },
        };
        break;
      case 'high_cost':
        presetFilters = {
          // This would need to be implemented based on cost thresholds
          stato: ['Attivo'],
        };
        break;
      case 'expiring_soon':
        const nextMonth = new Date(today);
        nextMonth.setMonth(today.getMonth() + 1);
        presetFilters = {
          dateFilter: {
            dateField: 'DATA_FINE',
            startDate: today,
            endDate: nextMonth,
          },
          stato: ['Attivo'],
        };
        break;
    }

    setLocalFilters(presetFilters);
    onFiltersChange(presetFilters);
  };

  const updateMultiSelectFilter = (
    field: keyof FilterOptions,
    value: string,
    checked: boolean
  ) => {
    const currentValues = (localFilters[field] as string[]) || [];
    const newValues = checked
      ? [...currentValues, value]
      : currentValues.filter(v => v !== value);

    setLocalFilters({
      ...localFilters,
      [field]: newValues.length > 0 ? newValues : undefined,
    });
  };

  const updateDateFilter = (updates: Partial<DateFilterOptions>) => {
    const currentDateFilter = localFilters.dateFilter || {
      dateField: 'DATA_FINE',
      startDate: null,
      endDate: null,
    };

    setLocalFilters({
      ...localFilters,
      dateFilter: {
        ...currentDateFilter,
        ...updates,
      },
    });
  };

  const getActiveFilterCount = () => {
    let count = 0;
    Object.entries(localFilters).forEach(([key, value]) => {
      if (key === 'dateFilter') {
        const dateFilter = value as DateFilterOptions;
        if (dateFilter?.startDate || dateFilter?.endDate) count++;
      } else if (Array.isArray(value) && value.length > 0) {
        count++;
      }
    });
    return count;
  };

  if (!isOpen) {
    return (
      <Button
        variant="outline"
        onClick={onToggle}
        className="fixed top-4 right-4 z-50 shadow-lg"
      >
        <FilterIcon className="h-4 w-4 mr-2" />
        Filtri
        {getActiveFilterCount() > 0 && (
          <Badge variant="secondary" className="ml-2">
            {getActiveFilterCount()}
          </Badge>
        )}
      </Button>
    );
  }

  return (
    <div className="fixed inset-y-0 right-0 w-96 bg-white shadow-xl z-50 overflow-y-auto">
      <Card className="h-full rounded-none border-0">
        <CardHeader className="sticky top-0 bg-white z-10 border-b">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FilterIcon className="h-5 w-5" />
              Filtri Avanzati
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onToggle}>
              <XIcon className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6 p-6">
          {/* Quick Presets */}
          <div>
            <h3 className="text-sm font-medium mb-3">⚡ Filtri Rapidi</h3>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => applyPreset('last_7_days')}
                className="justify-start"
              >
                <ClockIcon className="h-4 w-4 mr-2" />
                Ultimi 7 giorni
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => applyPreset('this_month')}
                className="justify-start"
              >
                <CalendarIcon className="h-4 w-4 mr-2" />
                Questo mese
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => applyPreset('high_cost')}
                className="justify-start"
              >
                <DollarSignIcon className="h-4 w-4 mr-2" />
                Alto costo
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => applyPreset('expiring_soon')}
                className="justify-start"
              >
                <TrendingUpIcon className="h-4 w-4 mr-2" />
                In scadenza
              </Button>
            </div>
          </div>

          <Separator />

          {/* Date Filter */}
          <div>
            <h3 className="text-sm font-medium mb-3">📅 Filtro Date</h3>
            
            <div className="space-y-3">
              <div>
                <label className="text-xs text-muted-foreground">Campo Data</label>
                <Select
                  value={localFilters.dateFilter?.dateField || 'DATA_FINE'}
                  onValueChange={(value: 'DATA_FINE' | 'DATA_EFFETTIVA') =>
                    updateDateFilter({ dateField: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DATA_FINE">Data Fine Contratto</SelectItem>
                    <SelectItem value="DATA_EFFETTIVA">Data Consegna</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-xs text-muted-foreground">Data Inizio</label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {localFilters.dateFilter?.startDate
                          ? format(localFilters.dateFilter.startDate, 'dd/MM/yyyy', { locale: it })
                          : 'Seleziona'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={localFilters.dateFilter?.startDate || undefined}
                        onSelect={(date) => updateDateFilter({ startDate: date || null })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div>
                  <label className="text-xs text-muted-foreground">Data Fine</label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {localFilters.dateFilter?.endDate
                          ? format(localFilters.dateFilter.endDate, 'dd/MM/yyyy', { locale: it })
                          : 'Seleziona'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={localFilters.dateFilter?.endDate || undefined}
                        onSelect={(date) => updateDateFilter({ endDate: date || null })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Multi-select filters */}
          {Object.entries(availableOptions).map(([field, options]) => (
            <div key={field}>
              <h3 className="text-sm font-medium mb-3 capitalize">
                {field === 'stato' ? '🚗 Stato' :
                 field === 'categoria' ? '📂 Categoria' :
                 field === 'gruppo' ? '👥 Gruppo' :
                 field === 'proprieta' ? '🏢 Proprietà' :
                 field === 'parcoSede' ? '📍 Parco/Sede' :
                 field === 'assicurazione' ? '🛡️ Assicurazione' : field}
              </h3>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {options.map((option) => (
                  <div key={option} className="flex items-center space-x-2">
                    <Checkbox
                      id={`${field}-${option}`}
                      checked={(localFilters[field as keyof FilterOptions] as string[] || []).includes(option)}
                      onCheckedChange={(checked) =>
                        updateMultiSelectFilter(field as keyof FilterOptions, option, checked as boolean)
                      }
                    />
                    <label
                      htmlFor={`${field}-${option}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {option}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          ))}

          <Separator />

          {/* Action buttons */}
          <div className="space-y-2">
            <Button onClick={applyFilters} className="w-full">
              <ZapIcon className="h-4 w-4 mr-2" />
              Applica Filtri
            </Button>
            <Button variant="outline" onClick={clearAllFilters} className="w-full">
              <RefreshCwIcon className="h-4 w-4 mr-2" />
              Cancella Tutti
            </Button>
          </div>

          {/* Active filters display */}
          {getActiveFilterCount() > 0 && (
            <div>
              <h3 className="text-sm font-medium mb-2">Filtri Attivi</h3>
              <div className="flex flex-wrap gap-1">
                {Object.entries(localFilters).map(([key, value]) => {
                  if (key === 'dateFilter' && value) {
                    const dateFilter = value as DateFilterOptions;
                    if (dateFilter.startDate || dateFilter.endDate) {
                      return (
                        <Badge key={key} variant="secondary">
                          Date: {dateFilter.dateField}
                        </Badge>
                      );
                    }
                  } else if (Array.isArray(value) && value.length > 0) {
                    return (
                      <Badge key={key} variant="secondary">
                        {key}: {value.length}
                      </Badge>
                    );
                  }
                  return null;
                })}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
