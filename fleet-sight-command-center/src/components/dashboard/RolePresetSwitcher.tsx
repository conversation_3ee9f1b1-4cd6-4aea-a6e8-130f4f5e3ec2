import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, User, DollarSign, Settings } from "lucide-react";
import { useDashboardStore, DashboardRole } from "@/stores/dashboardStore";

const roleConfig = {
  manager: {
    label: "Manager",
    icon: User,
    description: "High-level overview and fleet performance",
    color: "bg-primary text-primary-foreground"
  },
  finance: {
    label: "Finance",
    icon: DollarSign,
    description: "Cost analysis and budget tracking",
    color: "bg-success text-success-foreground"
  },
  operations: {
    label: "Operations",
    icon: Settings,
    description: "Maintenance schedules and vehicle status",
    color: "bg-warning text-warning-foreground"
  }
};

export function RolePresetSwitcher() {
  const { currentRole, setRole } = useDashboardStore();
  const currentConfig = roleConfig[currentRole];
  const CurrentIcon = currentConfig.icon;

  const handleRoleChange = (role: DashboardRole) => {
    setRole(role);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="gap-2">
          <CurrentIcon className="w-4 h-4" />
          {currentConfig.label}
          <Badge variant="secondary" className={currentConfig.color}>
            Active
          </Badge>
          <ChevronDown className="w-4 h-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel>Dashboard Presets</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {Object.entries(roleConfig).map(([role, config]) => {
          const Icon = config.icon;
          const isActive = currentRole === role;
          
          return (
            <DropdownMenuItem
              key={role}
              onClick={() => handleRoleChange(role as DashboardRole)}
              className="flex items-start gap-3 p-3 cursor-pointer"
            >
              <Icon className="w-4 h-4 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{config.label}</span>
                  {isActive && (
                    <Badge variant="secondary" className="text-xs">
                      Current
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {config.description}
                </p>
              </div>
            </DropdownMenuItem>
          );
        })}
        
        <DropdownMenuSeparator />
        <DropdownMenuItem className="text-sm text-muted-foreground">
          Custom layouts coming soon...
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}