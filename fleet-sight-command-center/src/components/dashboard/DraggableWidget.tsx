import { ReactNode } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { GripVertical, X, Settings } from "lucide-react";
import { cn } from "@/lib/utils";
import { Widget } from "@/stores/dashboardStore";

interface DraggableWidgetProps {
  widget: Widget;
  children: ReactNode;
  onRemove?: (widgetId: string) => void;
  onConfigure?: (widgetId: string) => void;
  className?: string;
}

export function DraggableWidget({ 
  widget, 
  children, 
  onRemove, 
  onConfigure,
  className 
}: DraggableWidgetProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: widget.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const getSizeClasses = (size: Widget['size']) => {
    switch (size) {
      case 'small': return 'col-span-1 row-span-1';
      case 'medium': return 'col-span-2 row-span-1';
      case 'large': return 'col-span-2 row-span-2';
      default: return 'col-span-1 row-span-1';
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "group relative",
        getSizeClasses(widget.size),
        isDragging && "opacity-50 z-50",
        className
      )}
    >
      <Card className="h-full relative overflow-hidden">
        {/* Widget Controls */}
        <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="flex gap-1">
            {onConfigure && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 bg-background/80 backdrop-blur-sm"
                onClick={() => onConfigure(widget.id)}
              >
                <Settings className="w-3 h-3" />
              </Button>
            )}
            {onRemove && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 bg-background/80 backdrop-blur-sm hover:bg-destructive hover:text-destructive-foreground"
                onClick={() => onRemove(widget.id)}
              >
                <X className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>

        {/* Drag Handle */}
        <div
          {...attributes}
          {...listeners}
          className="absolute top-2 left-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing"
        >
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 bg-background/80 backdrop-blur-sm"
          >
            <GripVertical className="w-3 h-3" />
          </Button>
        </div>

        {/* Widget Content */}
        <div className="h-full">
          {children}
        </div>
      </Card>
    </div>
  );
}