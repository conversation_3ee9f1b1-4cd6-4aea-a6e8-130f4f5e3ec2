import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, TrendingDown, Minus, LucideIcon } from 'lucide-react';

interface FleetKPICardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: LucideIcon;
  trend?: {
    type: 'positive' | 'negative' | 'neutral';
    value: string;
    label?: string;
  };
  format?: 'currency' | 'percentage' | 'number';
  className?: string;
}

export function FleetKPICard({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  format = 'number',
  className = '',
}: FleetKPICardProps) {
  const formatValue = (val: string | number) => {
    const numValue = typeof val === 'string' ? parseFloat(val) : val;
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('it-IT', {
          style: 'currency',
          currency: 'EUR',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(numValue);
      case 'percentage':
        return `${numValue.toFixed(1)}%`;
      default:
        return new Intl.NumberFormat('it-IT').format(numValue);
    }
  };

  const getTrendIcon = () => {
    switch (trend?.type) {
      case 'positive':
        return <TrendingUp className="h-4 w-4" />;
      case 'negative':
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <Minus className="h-4 w-4" />;
    }
  };

  const getTrendColor = () => {
    switch (trend?.type) {
      case 'positive':
        return 'text-green-600 bg-green-50';
      case 'negative':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <Card className={`transition-all duration-200 hover:shadow-md hover:-translate-y-1 ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
          {Icon && <Icon className="h-4 w-4" />}
          {title}
        </CardTitle>
        {trend && (
          <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getTrendColor()}`}>
            {getTrendIcon()}
            <span>{trend.value}</span>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-900 mb-1">
          {formatValue(value)}
        </div>
        {subtitle && (
          <p className="text-xs text-muted-foreground">
            {subtitle}
          </p>
        )}
        {trend?.label && (
          <p className="text-xs text-muted-foreground mt-1">
            {trend.label}
          </p>
        )}
      </CardContent>
    </Card>
  );
}

// Specialized KPI cards for fleet management
interface FleetCostKPIProps {
  preTaxAmount: number;
  withVATAmount: number;
  title: string;
  icon?: LucideIcon;
  trend?: {
    type: 'positive' | 'negative' | 'neutral';
    value: string;
  };
}

export function FleetCostKPI({ preTaxAmount, withVATAmount, title, icon, trend }: FleetCostKPIProps) {
  const formatCurrency = (amount: number) => 
    new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);

  return (
    <FleetKPICard
      title={title}
      value={formatCurrency(preTaxAmount)}
      subtitle={`(${formatCurrency(withVATAmount)} con IVA)`}
      icon={icon}
      trend={trend}
      format="number" // We handle formatting manually
    />
  );
}

interface SavingsKPIProps {
  totalSavings: number;
  totalSavingsWithVAT: number;
  vehicleCount: number;
  daysSaved: number;
  title: string;
  icon?: LucideIcon;
}

export function SavingsKPI({ 
  totalSavings, 
  totalSavingsWithVAT, 
  vehicleCount, 
  daysSaved, 
  title, 
  icon 
}: SavingsKPIProps) {
  const formatCurrency = (amount: number) => 
    new Intl.NumberFormat('it-IT', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);

  const subtitle = vehicleCount > 0 
    ? `${vehicleCount} veicoli • ${daysSaved} giorni risparmiati`
    : 'Nessun risparmio rilevato';

  return (
    <FleetKPICard
      title={title}
      value={formatCurrency(totalSavings)}
      subtitle={`(${formatCurrency(totalSavingsWithVAT)} con IVA) • ${subtitle}`}
      icon={icon}
      trend={vehicleCount > 0 ? {
        type: 'positive',
        value: `${vehicleCount} veicoli`,
        label: 'Consegne anticipate'
      } : undefined}
      format="number"
    />
  );
}

// Grid layout for KPI cards
interface FleetKPIGridProps {
  children: React.ReactNode;
  columns?: 2 | 3 | 4;
  className?: string;
}

export function FleetKPIGrid({ children, columns = 4, className = '' }: FleetKPIGridProps) {
  const gridCols = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={`grid gap-4 ${gridCols[columns]} ${className}`}>
      {children}
    </div>
  );
}
