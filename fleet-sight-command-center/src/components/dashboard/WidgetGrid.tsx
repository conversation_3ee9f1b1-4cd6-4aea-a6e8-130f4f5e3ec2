import { useEffect, useState } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from "@dnd-kit/sortable";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Plus, BarChart, Calendar, PieChart, Map } from "lucide-react";
import { useDashboardStore, Widget } from "@/stores/dashboardStore";
import { DraggableWidget } from "./DraggableWidget";
import { KPICard } from "./KPICard";
import { TimelineChart } from "../charts/TimelineChart";
import { WaterfallChart } from "../charts/WaterfallChart";
import { mockDataService } from "@/services/mockDataService";
import { RolePresetSwitcher } from "./RolePresetSwitcher";

export function WidgetGrid() {
  const { widgets, addWidget, removeWidget, updateWidget, moveWidget } = useDashboardStore();
  const [timelineData, setTimelineData] = useState([]);
  const [waterfallData, setWaterfallData] = useState([]);
  const [utilizationData, setUtilizationData] = useState([]);
  const [costData, setCostData] = useState([]);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    // Load mock data
    setTimelineData(mockDataService.generateTimelineData());
    setWaterfallData(mockDataService.generateWaterfallData());
    setUtilizationData(mockDataService.generateUtilizationData());
    setCostData(mockDataService.generateCostBreakdownData());
  }, []);

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = widgets.findIndex((w) => w.id === active.id);
      const newIndex = widgets.findIndex((w) => w.id === over?.id);
      
      const reorderedWidgets = arrayMove(widgets, oldIndex, newIndex);
      // Update positions in store
      reorderedWidgets.forEach((widget, index) => {
        moveWidget(widget.id, { x: index % 3, y: Math.floor(index / 3) });
      });
    }
  };

  const handleAddWidget = (type: Widget['type']) => {
    const newWidget: Omit<Widget, 'id'> = {
      type,
      title: getWidgetTitle(type),
      size: type === 'timeline' || type === 'waterfall' ? 'large' : 'medium',
      position: { x: widgets.length % 3, y: Math.floor(widgets.length / 3) }
    };
    addWidget(newWidget);
  };

  const getWidgetTitle = (type: Widget['type']) => {
    switch (type) {
      case 'timeline': return 'Maintenance Timeline';
      case 'waterfall': return 'Cost Waterfall';
      case 'utilization': return 'Fleet Utilization';
      case 'cost-breakdown': return 'Cost Breakdown';
      case 'maintenance': return 'Maintenance Alerts';
      case 'locations': return 'Vehicle Locations';
      default: return 'Dashboard Widget';
    }
  };

  const renderWidget = (widget: Widget) => {
    switch (widget.type) {
      case 'timeline':
        return (
          <TimelineChart 
            data={timelineData} 
            title={widget.title}
            className="h-full"
          />
        );
      case 'waterfall':
        return (
          <WaterfallChart 
            data={waterfallData} 
            title={widget.title}
            className="h-full"
          />
        );
      case 'utilization':
        return (
          <div className="h-full bg-muted/30 rounded-lg flex items-center justify-center border-2 border-dashed border-muted">
            <p className="text-sm text-muted-foreground text-center">
              Fleet utilization chart will be implemented
            </p>
          </div>
        );
      case 'cost-breakdown':
        return (
          <div className="h-full bg-muted/30 rounded-lg flex items-center justify-center border-2 border-dashed border-muted">
            <p className="text-sm text-muted-foreground text-center">
              Cost breakdown chart will be implemented
            </p>
          </div>
        );
      case 'maintenance':
        return (
          <div className="h-full bg-muted/30 rounded-lg flex items-center justify-center border-2 border-dashed border-muted">
            <p className="text-sm text-muted-foreground text-center">
              Maintenance alerts will be implemented
            </p>
          </div>
        );
      case 'locations':
        return (
          <div className="h-full bg-muted/30 rounded-lg flex items-center justify-center border-2 border-dashed border-muted">
            <p className="text-sm text-muted-foreground text-center">
              Vehicle locations map will be implemented
            </p>
          </div>
        );
      default:
        return (
          <div className="h-full bg-muted/30 rounded-lg flex items-center justify-center border-2 border-dashed border-muted">
            <p className="text-sm text-muted-foreground text-center">
              Widget content loading...
            </p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Dashboard Widgets</h2>
        <div className="flex gap-2">
          <RolePresetSwitcher />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Add Widget
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Available Widgets</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleAddWidget('timeline')}>
                <Calendar className="w-4 h-4 mr-2" />
                Maintenance Timeline
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAddWidget('waterfall')}>
                <BarChart className="w-4 h-4 mr-2" />
                Cost Waterfall
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAddWidget('utilization')}>
                <PieChart className="w-4 h-4 mr-2" />
                Fleet Utilization
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAddWidget('cost-breakdown')}>
                <BarChart className="w-4 h-4 mr-2" />
                Cost Breakdown
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAddWidget('locations')}>
                <Map className="w-4 h-4 mr-2" />
                Vehicle Locations
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={widgets.map(w => w.id)} strategy={rectSortingStrategy}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-fr">
            {widgets.map((widget) => (
              <DraggableWidget
                key={widget.id}
                widget={widget}
                onRemove={removeWidget}
                onConfigure={(id) => console.log('Configure widget:', id)}
              >
                {renderWidget(widget)}
              </DraggableWidget>
            ))}
          </div>
        </SortableContext>
      </DndContext>
      
      {widgets.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground mb-4">
            No widgets configured. Add your first widget to get started.
          </p>
          <Button variant="outline" onClick={() => handleAddWidget('timeline')}>
            <Plus className="w-4 h-4 mr-2" />
            Add Timeline Widget
          </Button>
        </div>
      )}
    </div>
  );
}