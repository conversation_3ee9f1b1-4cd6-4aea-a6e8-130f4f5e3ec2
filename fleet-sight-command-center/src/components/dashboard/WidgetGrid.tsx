import { useEffect, useState } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from "@dnd-kit/sortable";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Plus, BarChart, Calendar, PieChart, Map, TrendingUp } from "lucide-react";
import { useDashboardStore, Widget } from "@/stores/dashboardStore";
import { DraggableWidget } from "./DraggableWidget";
import { KPICard } from "./KPICard";
import { TimelineChart } from "../charts/TimelineChart";
import { WaterfallChart } from "../charts/WaterfallChart";
import { FleetCompositionChart } from "../charts/FleetCompositionChart";
import { CostAnalysisChart } from "../charts/CostAnalysisChart";
import { SavingsAnalysisChart } from "../charts/SavingsAnalysisChart";
import { LocationDistributionChart } from "../charts/LocationDistributionChart";
import { ContractExpirationChart } from "../charts/ContractExpirationChart";
import fleetDataService, { FilterOptions } from "@/services/fleetDataService";
import { RolePresetSwitcher } from "./RolePresetSwitcher";

export function WidgetGrid() {
  const { widgets, addWidget, removeWidget, updateWidget, moveWidget } = useDashboardStore();
  const [filters, setFilters] = useState<FilterOptions>({});
  const [isLoading, setIsLoading] = useState(true);
  const [fleetCompositionData, setFleetCompositionData] = useState<any[]>([]);
  const [costAnalysisData, setCostAnalysisData] = useState<any[]>([]);
  const [savingsAnalysisData, setSavingsAnalysisData] = useState<any[]>([]);
  const [locationData, setLocationData] = useState<any[]>([]);
  const [contractExpirationData, setContractExpirationData] = useState<any[]>([]);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    loadFleetData();
  }, []);

  useEffect(() => {
    if (!isLoading) {
      updateChartData();
    }
  }, [filters, isLoading]);

  const loadFleetData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/data/RegistroFLOTTA_Master.csv');
      const csvContent = await response.text();
      await fleetDataService.loadData(csvContent);
      setIsLoading(false);
    } catch (error) {
      console.error('Error loading fleet data:', error);
      setIsLoading(false);
    }
  };

  const updateChartData = () => {
    setFleetCompositionData(fleetDataService.getFleetCompositionData(filters));
    setCostAnalysisData(fleetDataService.getCostAnalysisData(filters));
    setSavingsAnalysisData(fleetDataService.getSavingsAnalysisData(filters));
    setLocationData(fleetDataService.getLocationDistributionData(filters));
    setContractExpirationData(fleetDataService.getContractExpirationData(filters));
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = widgets.findIndex((w) => w.id === active.id);
      const newIndex = widgets.findIndex((w) => w.id === over?.id);
      
      const reorderedWidgets = arrayMove(widgets, oldIndex, newIndex);
      // Update positions in store
      reorderedWidgets.forEach((widget, index) => {
        moveWidget(widget.id, { x: index % 3, y: Math.floor(index / 3) });
      });
    }
  };

  const handleAddWidget = (type: Widget['type']) => {
    const newWidget: Omit<Widget, 'id'> = {
      type,
      title: getWidgetTitle(type),
      size: type === 'timeline' || type === 'waterfall' ? 'large' : 'medium',
      position: { x: widgets.length % 3, y: Math.floor(widgets.length / 3) }
    };
    addWidget(newWidget);
  };

  const getWidgetTitle = (type: Widget['type']) => {
    switch (type) {
      case 'timeline': return 'Contract Expiration Timeline';
      case 'waterfall': return 'Cost Waterfall';
      case 'utilization': return 'Fleet Composition';
      case 'cost-breakdown': return 'Cost Analysis by Category';
      case 'maintenance': return 'Savings Analysis';
      case 'locations': return 'Location Distribution';
      default: return 'Dashboard Widget';
    }
  };

  const renderWidget = (widget: Widget) => {
    if (isLoading) {
      return (
        <div className="h-full bg-muted/30 rounded-lg flex items-center justify-center border-2 border-dashed border-muted">
          <p className="text-sm text-muted-foreground text-center">
            Loading fleet data...
          </p>
        </div>
      );
    }

    switch (widget.type) {
      case 'timeline':
        return (
          <ContractExpirationChart
            data={contractExpirationData}
            title={widget.title}
            className="h-full"
          />
        );
      case 'waterfall':
        return (
          <div className="h-full bg-muted/30 rounded-lg flex items-center justify-center border-2 border-dashed border-muted">
            <p className="text-sm text-muted-foreground text-center">
              Waterfall chart will be implemented in Phase 2
            </p>
          </div>
        );
      case 'utilization':
        return (
          <FleetCompositionChart
            data={fleetCompositionData}
            title={widget.title}
            className="h-full"
          />
        );
      case 'cost-breakdown':
        return (
          <CostAnalysisChart
            data={costAnalysisData}
            title={widget.title}
            className="h-full"
          />
        );
      case 'maintenance':
        return (
          <SavingsAnalysisChart
            data={savingsAnalysisData}
            title={widget.title}
            className="h-full"
          />
        );
      case 'locations':
        return (
          <LocationDistributionChart
            data={locationData}
            title={widget.title}
            className="h-full"
          />
        );
      default:
        return (
          <div className="h-full bg-muted/30 rounded-lg flex items-center justify-center border-2 border-dashed border-muted">
            <p className="text-sm text-muted-foreground text-center">
              Widget content loading...
            </p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Dashboard Widgets</h2>
        <div className="flex gap-2">
          <RolePresetSwitcher />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Plus className="w-4 h-4 mr-2" />
                Add Widget
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Available Widgets</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleAddWidget('timeline')}>
                <Calendar className="w-4 h-4 mr-2" />
                Contract Expiration Timeline
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAddWidget('utilization')}>
                <PieChart className="w-4 h-4 mr-2" />
                Fleet Composition
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAddWidget('cost-breakdown')}>
                <BarChart className="w-4 h-4 mr-2" />
                Cost Analysis by Category
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAddWidget('maintenance')}>
                <TrendingUp className="w-4 h-4 mr-2" />
                Savings Analysis
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleAddWidget('locations')}>
                <Map className="w-4 h-4 mr-2" />
                Location Distribution
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={widgets.map(w => w.id)} strategy={rectSortingStrategy}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-fr">
            {widgets.map((widget) => (
              <DraggableWidget
                key={widget.id}
                widget={widget}
                onRemove={removeWidget}
                onConfigure={(id) => console.log('Configure widget:', id)}
              >
                {renderWidget(widget)}
              </DraggableWidget>
            ))}
          </div>
        </SortableContext>
      </DndContext>
      
      {widgets.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground mb-4">
            No widgets configured. Add your first widget to get started.
          </p>
          <Button variant="outline" onClick={() => handleAddWidget('timeline')}>
            <Plus className="w-4 h-4 mr-2" />
            Add Timeline Widget
          </Button>
        </div>
      )}
    </div>
  );
}