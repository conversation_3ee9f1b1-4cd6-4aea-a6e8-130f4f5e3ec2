import { useLocation, <PERSON> } from "react-router-dom";
import { ChevronRight, Home } from "lucide-react";

export function Breadcrumbs() {
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter((x) => x);

  const breadcrumbNameMap: Record<string, string> = {
    '': 'Overview',
    'fleet': 'Fleet Analysis',
    'costs': 'Cost Management',
    'data': 'Data',
    'settings': 'Settings',
  };

  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
      <Link 
        to="/" 
        className="hover:text-foreground transition-colors flex items-center"
      >
        <Home className="w-4 h-4" />
      </Link>
      
      {pathnames.length > 0 && (
        <>
          <ChevronRight className="w-4 h-4" />
          {pathnames.map((value, index) => {
            const to = `/${pathnames.slice(0, index + 1).join('/')}`;
            const isLast = index === pathnames.length - 1;
            
            return (
              <div key={to} className="flex items-center space-x-1">
                {isLast ? (
                  <span className="text-foreground font-medium">
                    {breadcrumbNameMap[value] || value}
                  </span>
                ) : (
                  <>
                    <Link 
                      to={to} 
                      className="hover:text-foreground transition-colors"
                    >
                      {breadcrumbNameMap[value] || value}
                    </Link>
                    <ChevronRight className="w-4 h-4" />
                  </>
                )}
              </div>
            );
          })}
        </>
      )}
    </nav>
  );
}