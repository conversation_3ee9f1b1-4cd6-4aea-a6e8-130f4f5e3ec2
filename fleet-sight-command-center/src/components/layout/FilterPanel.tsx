import { useState } from "react";
import { X, Calendar, MapPin, DollarSign, Car, Filter } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";

interface FilterPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export function FilterPanel({ isOpen, onClose }: FilterPanelProps) {
  const [activeFilters, setActiveFilters] = useState<string[]>([]);

  const addFilter = (filter: string) => {
    if (!activeFilters.includes(filter)) {
      setActiveFilters([...activeFilters, filter]);
    }
  };

  const removeFilter = (filter: string) => {
    setActiveFilters(activeFilters.filter(f => f !== filter));
  };

  const clearAllFilters = () => {
    setActiveFilters([]);
  };

  if (!isOpen) return null;

  return (
    <div className={`
      fixed lg:relative top-0 right-0 h-full w-80 bg-background border-l z-40 transform transition-transform duration-300
      ${isOpen ? 'translate-x-0' : 'translate-x-full lg:translate-x-0'}
      lg:block
    `}>
      <Card className="h-full rounded-none border-0 shadow-lg lg:shadow-none">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <CardTitle className="text-lg">Filters</CardTitle>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
          {activeFilters.length > 0 && (
            <div className="flex items-center justify-between">
              <div className="flex flex-wrap gap-1">
                {activeFilters.map((filter, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {filter}
                    <button
                      onClick={() => removeFilter(filter)}
                      className="ml-1 hover:text-destructive"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
              <Button variant="ghost" size="sm" onClick={clearAllFilters} className="text-xs">
                Clear all
              </Button>
            </div>
          )}
        </CardHeader>
        <Separator />
        <CardContent className="space-y-6 p-4 overflow-y-auto">
          {/* Quick Presets */}
          <div>
            <h3 className="text-sm font-medium mb-3 flex items-center">
              <Calendar className="w-4 h-4 mr-2" />
              Quick Presets
            </h3>
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" size="sm" onClick={() => addFilter("Last 7 days")}>
                Last 7 days
              </Button>
              <Button variant="outline" size="sm" onClick={() => addFilter("This month")}>
                This month
              </Button>
              <Button variant="outline" size="sm" onClick={() => addFilter("High cost")}>
                High cost
              </Button>
              <Button variant="outline" size="sm" onClick={() => addFilter("Maintenance due")}>
                Due service
              </Button>
            </div>
          </div>

          <Separator />

          {/* Date Range */}
          <div>
            <h3 className="text-sm font-medium mb-3">Date Range</h3>
            <div className="space-y-2">
              <Select onValueChange={(value) => addFilter(`Period: ${value}`)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select time period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          {/* Vehicle Status */}
          <div>
            <h3 className="text-sm font-medium mb-3 flex items-center">
              <Car className="w-4 h-4 mr-2" />
              Vehicle Status
            </h3>
            <div className="space-y-2">
              {["Active", "Maintenance", "Reserved", "Inactive"].map((status) => (
                <div key={status} className="flex items-center space-x-2">
                  <Checkbox
                    id={status}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        addFilter(`Status: ${status}`);
                      } else {
                        removeFilter(`Status: ${status}`);
                      }
                    }}
                  />
                  <label htmlFor={status} className="text-sm">
                    {status}
                  </label>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Location */}
          <div>
            <h3 className="text-sm font-medium mb-3 flex items-center">
              <MapPin className="w-4 h-4 mr-2" />
              Location
            </h3>
            <Select onValueChange={(value) => addFilter(`Location: ${value}`)}>
              <SelectTrigger>
                <SelectValue placeholder="Select region" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="north">North Region</SelectItem>
                <SelectItem value="south">South Region</SelectItem>
                <SelectItem value="east">East Region</SelectItem>
                <SelectItem value="west">West Region</SelectItem>
                <SelectItem value="central">Central Region</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Cost Range */}
          <div>
            <h3 className="text-sm font-medium mb-3 flex items-center">
              <DollarSign className="w-4 h-4 mr-2" />
              Cost Range
            </h3>
            <Select onValueChange={(value) => addFilter(`Cost: ${value}`)}>
              <SelectTrigger>
                <SelectValue placeholder="Select cost range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">$0 - $1,000</SelectItem>
                <SelectItem value="medium">$1,000 - $5,000</SelectItem>
                <SelectItem value="high">$5,000 - $10,000</SelectItem>
                <SelectItem value="premium">$10,000+</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}