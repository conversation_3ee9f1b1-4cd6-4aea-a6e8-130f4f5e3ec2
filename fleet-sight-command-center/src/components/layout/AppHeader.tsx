import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useDashboardStore } from "@/stores/dashboardStore";

interface AppHeaderProps {
  onFilterToggle?: () => void;
  showFilterPanel?: boolean;
}

export function AppHeader({ onFilterToggle, showFilterPanel }: AppHeaderProps) {
  const { activeFilters } = useDashboardStore();
  
  const toggleTheme = () => {
    document.documentElement.classList.toggle("dark");
  };

  return (
    <header className="h-16 border-b bg-header-bg px-4 flex items-center justify-between sticky top-0 z-50">
      <div className="flex items-center space-x-4">
        <SidebarTrigger className="lg:hidden" />
        <div className="hidden lg:block">
          <h1 className="text-xl font-semibold text-foreground">Fleet Management Dashboard</h1>
        </div>
      </div>

      <div className="flex items-center space-x-3">
        {/* Filter Toggle */}
        <Button
          variant={showFilterPanel ? "default" : "outline"}
          size="sm"
          onClick={onFilterToggle}
          className="hidden md:flex items-center space-x-2"
        >
          <Filter className="w-4 h-4" />
          <span>Filters</span>
          {Object.keys(activeFilters).length > 0 && (
            <Badge variant="secondary" className="ml-1">
              {Object.keys(activeFilters).length}
            </Badge>
          )}
        </Button>

        {/* Notifications */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="w-4 h-4" />
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
              >
                3
              </Badge>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80">
            <div className="p-2 font-medium text-sm">Notifications</div>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="p-3 space-y-1">
              <div className="font-medium text-sm">Vehicle Maintenance Alert</div>
              <div className="text-xs text-muted-foreground">Fleet ID FL-2847 requires immediate service</div>
            </DropdownMenuItem>
            <DropdownMenuItem className="p-3 space-y-1">
              <div className="font-medium text-sm">Cost Threshold Exceeded</div>
              <div className="text-xs text-muted-foreground">Monthly fuel costs are 15% above budget</div>
            </DropdownMenuItem>
            <DropdownMenuItem className="p-3 space-y-1">
              <div className="font-medium text-sm">Contract Renewal Due</div>
              <div className="text-xs text-muted-foreground">3 lease contracts expire this month</div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Theme Toggle */}
        <Button variant="ghost" size="sm" onClick={toggleTheme}>
          <Sun className="w-4 h-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute w-4 h-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
        </Button>

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 rounded-full">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder-avatar.jpg" alt="User" />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <div className="p-2">
              <div className="font-medium">John Doe</div>
              <div className="text-sm text-muted-foreground">Fleet Manager</div>
            </div>
            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <User className="w-4 h-4 mr-2" />
              Profile
            </DropdownMenuItem>
            <DropdownMenuItem>Settings</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Log out</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}