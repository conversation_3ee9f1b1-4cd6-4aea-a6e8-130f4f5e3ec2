import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Bot, 
  Send, 
  User, 
  Lightbulb, 
  TrendingUp, 
  AlertCircle,
  Loader2,
  Settings,
  MessageSquare,
  Zap,
  Brain,
  BarChart3
} from 'lucide-react';
import aiChatService from '@/services/aiChatService';
import { FleetInsightResponse } from '@/types/fleet';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  insights?: string[];
  recommendations?: string[];
  dataReferences?: string[];
}

interface AIChatInterfaceProps {
  isOpen: boolean;
  onToggle: () => void;
  context?: string;
  className?: string;
}

export function AIChatInterface({ isOpen, onToggle, context = 'overview', className = '' }: AIChatInterfaceProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [aiStatus, setAiStatus] = useState<'connected' | 'disconnected' | 'checking'>('checking');
  const [isInitialized, setIsInitialized] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Quick action prompts based on context
  const quickPrompts = {
    overview: [
      "Analizza le prestazioni della flotta",
      "Mostra opportunità di risparmio",
      "Quali sono i veicoli in scadenza?",
      "Suggerimenti per ottimizzare i costi"
    ],
    costs: [
      "Come ridurre i costi mensili?",
      "Analizza i risparmi dalle consegne anticipate",
      "Veicoli con costi elevati",
      "Proiezioni di spesa annuale"
    ],
    fleet: [
      "Distribuzione veicoli per categoria",
      "Analisi utilizzo per località",
      "Veicoli sottoutilizzati",
      "Confronto prestazioni modelli"
    ]
  };

  useEffect(() => {
    initializeAI();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const initializeAI = async () => {
    setAiStatus('checking');
    try {
      const status = await aiChatService.checkOllamaStatus();
      if (status.available) {
        const initialized = await aiChatService.initialize();
        setIsInitialized(initialized);
        setAiStatus('connected');
        
        // Add welcome message
        const welcomeMessage: ChatMessage = {
          id: 'welcome',
          role: 'assistant',
          content: `🚀 Ciao! Sono il tuo assistente AI per la gestione della flotta.\n\nPosso aiutarti con:\n• Analisi delle prestazioni\n• Ottimizzazione dei costi\n• Raccomandazioni strategiche\n• Insights sui dati\n\nCosa vorresti sapere sulla tua flotta?`,
          timestamp: new Date(),
          insights: ['AI abilitato e pronto'],
          recommendations: ['Prova a chiedere informazioni sui costi o sulle prestazioni'],
          dataReferences: []
        };
        setMessages([welcomeMessage]);
      } else {
        setAiStatus('disconnected');
        setIsInitialized(false);
        
        // Add offline message
        const offlineMessage: ChatMessage = {
          id: 'offline',
          role: 'assistant',
          content: `⚠️ AI non disponibile\n\nOllama non è configurato o non è in esecuzione.\n\nPer abilitare l'AI:\n1. Verifica che Ollama sia in esecuzione: \`ollama serve\`\n2. Assicurati che il modello sia disponibile: \`ollama pull llama3.2\`\n3. Verifica connessione su localhost:11434\n\nNel frattempo posso fornirti risposte basic sui dati.`,
          timestamp: new Date(),
          insights: ['AI non configurato'],
          recommendations: ['Configura Ollama per funzionalità complete'],
          dataReferences: []
        };
        setMessages([offlineMessage]);
      }
    } catch (error) {
      setAiStatus('disconnected');
      setIsInitialized(false);
      console.error('Failed to initialize AI:', error);
    }
  };

  const sendMessage = async (messageText?: string) => {
    const text = messageText || inputMessage.trim();
    if (!text) return;

    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      role: 'user',
      content: text,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response: FleetInsightResponse = await aiChatService.sendMessage(text, context);
      
      const assistantMessage: ChatMessage = {
        id: `assistant_${Date.now()}`,
        role: 'assistant',
        content: response.answer,
        timestamp: new Date(),
        insights: response.insights,
        recommendations: response.recommendations,
        dataReferences: response.dataReferences
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        role: 'assistant',
        content: `❌ Errore durante l'elaborazione della richiesta.\n\n${error instanceof Error ? error.message : 'Errore sconosciuto'}`,
        timestamp: new Date(),
        insights: ['Errore di comunicazione'],
        recommendations: ['Riprova o controlla la configurazione'],
        dataReferences: []
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const clearChat = () => {
    setMessages([]);
    aiChatService.clearConversation();
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('it-IT', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (!isOpen) {
    return (
      <Button
        onClick={onToggle}
        className={`fixed bottom-6 right-6 w-14 h-14 rounded-full shadow-lg z-50 ${className}`}
        size="icon"
      >
        <Bot className="h-6 w-6" />
      </Button>
    );
  }

  return (
    <Card className={`fixed bottom-6 right-6 w-96 h-[600px] shadow-xl z-50 flex flex-col ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
        <div className="flex items-center space-x-2">
          <Bot className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg">Fleet AI Assistant</CardTitle>
          <Badge variant={aiStatus === 'connected' ? 'default' : 'secondary'} className="text-xs">
            {aiStatus === 'connected' ? 'Online' : aiStatus === 'checking' ? 'Checking...' : 'Offline'}
          </Badge>
        </div>
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={clearChat}
            disabled={messages.length === 0}
          >
            <MessageSquare className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
          >
            ✕
          </Button>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col min-h-0 space-y-4 p-4">
        {/* Quick Actions */}
        {messages.length <= 1 && (
          <div className="space-y-2">
            <div className="text-sm font-medium text-muted-foreground">Domande rapide:</div>
            <div className="grid grid-cols-1 gap-2">
              {(quickPrompts[context as keyof typeof quickPrompts] || quickPrompts.overview).map((prompt, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="justify-start text-left h-auto p-2 text-xs"
                  onClick={() => sendMessage(prompt)}
                  disabled={isLoading}
                >
                  <Zap className="h-3 w-3 mr-2 flex-shrink-0" />
                  {prompt}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Chat Messages */}
        <ScrollArea className="flex-1 min-h-0 pr-3 overflow-y-auto">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[85%] rounded-lg p-3 ${
                    message.role === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  }`}
                >
                  <div className="flex items-start space-x-2">
                    {message.role === 'assistant' && (
                      <Bot className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    )}
                    {message.role === 'user' && (
                      <User className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    )}
                    <div className="flex-1 space-y-2">
                      <div className="text-sm whitespace-pre-wrap">{message.content}</div>
                      
                      {/* Insights */}
                      {message.insights && message.insights.length > 0 && (
                        <div className="space-y-1">
                          <div className="flex items-center space-x-1">
                            <Lightbulb className="h-3 w-3" />
                            <span className="text-xs font-medium">Insights</span>
                          </div>
                          {message.insights.map((insight, index) => (
                            <Badge key={index} variant="secondary" className="text-xs mr-1">
                              {insight}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {/* Recommendations */}
                      {message.recommendations && message.recommendations.length > 0 && (
                        <div className="space-y-1">
                          <div className="flex items-center space-x-1">
                            <TrendingUp className="h-3 w-3" />
                            <span className="text-xs font-medium">Raccomandazioni</span>
                          </div>
                          {message.recommendations.map((rec, index) => (
                            <Badge key={index} variant="outline" className="text-xs mr-1">
                              {rec}
                            </Badge>
                          ))}
                        </div>
                      )}

                      {/* Data References */}
                      {message.dataReferences && message.dataReferences.length > 0 && (
                        <div className="space-y-1">
                          <div className="flex items-center space-x-1">
                            <BarChart3 className="h-3 w-3" />
                            <span className="text-xs font-medium">Dati</span>
                          </div>
                          {message.dataReferences.slice(0, 3).map((ref, index) => (
                            <div key={index} className="text-xs text-muted-foreground bg-background rounded px-2 py-1">
                              {ref}
                            </div>
                          ))}
                        </div>
                      )}

                      <div className="text-xs text-muted-foreground opacity-70">
                        {formatTimestamp(message.timestamp)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-muted rounded-lg p-3 max-w-[85%]">
                  <div className="flex items-center space-x-2">
                    <Bot className="h-4 w-4" />
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">Sto pensando...</span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="space-y-2">
          <Separator />
          <div className="flex space-x-2">
            <Input
              ref={inputRef}
              placeholder="Chiedi qualcosa sulla tua flotta..."
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={isLoading}
              className="flex-1"
            />
            <Button
              onClick={() => sendMessage()}
              disabled={isLoading || !inputMessage.trim()}
              size="icon"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          
          {aiStatus === 'disconnected' && (
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <AlertCircle className="h-3 w-3" />
              <span>AI limitato - configura Ollama per funzionalità complete</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default AIChatInterface;