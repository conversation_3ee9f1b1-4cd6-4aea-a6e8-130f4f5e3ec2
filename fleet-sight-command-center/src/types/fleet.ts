// Enhanced Fleet Data Types - Complete data model for comprehensive fleet management

export interface FleetVehicle {
  id: string;
  // Vehicle Identification
  acriss: string;                 // ACRISS car classification code
  categoria: string;              // Vehicle category (FURGONE MEDIO, SUV COMPATTO, etc.)
  gruppo: string;                 // Vehicle group (FURGONE, LARGE, ECONOMY)
  modello: string;                // Vehicle model (TOYOTA PROACE, PEUGEOT 2008, etc.)
  tipologia: string;              // Vehicle type (FURGONE, AUTO)
  trasmissione: string;           // Transmission type (MANUALE, AUTOMATICA)
  targa: string;                  // License plate number
  
  // Ownership and Contract
  proprieta: string;              // Owner/Leasing company (KINTO, LEASYS, etc.)
  acquisizione: string;           // Acquisition type (RENT, LEASING)
  durataInMesi: number;           // Contract duration in months
  kmInclusi: number;              // Included kilometers
  
  // Important Dates
  dataInizio: Date | null;        // Contract start date
  dataFine: Date | null;          // Contract end date
  riconsegna: Date | null;        // Return date
  dataEffettiva: Date | null;     // Actual delivery date
  
  // Status and Location
  stato: string;                  // Vehicle status (Attivo, etc.)
  parcoSede: string;              // Location/Site (TRAPANI PORTO, CASSINO, etc.)
  
  // Financial Information
  canoneImponibile: number;       // Taxable lease amount (monthly cost)
  canoneIvato: number;           // Lease amount with VAT
  coperturaAssicurativa: number;  // Insurance coverage amount
  
  // Additional Information
  note: string;                   // Notes
  rifRiepilogo: string;          // Summary reference
  controlloCanoni: string;       // Cost control field
  daVendere: string;             // For sale flag
  rentToRent: string;            // Rent to rent indicator
}

export interface FleetAnalytics {
  // Core Fleet Metrics
  totalVehicles: number;
  activeVehicles: number;
  utilizationRate: number;
  
  // Financial Metrics
  totalMonthlyCost: number;
  totalMonthlyCostWithVAT: number;
  averageCostPerVehicle: number;
  totalInsuranceCoverage: number;
  
  // Fleet Composition
  categoryBreakdown: CategoryBreakdown[];
  groupBreakdown: GroupBreakdown[];
  transmissionBreakdown: TransmissionBreakdown[];
  statusBreakdown: StatusBreakdown[];
  
  // Location Analytics
  locationBreakdown: LocationBreakdown[];
  topLocations: LocationSummary[];
  
  // Owner/Provider Analytics
  ownerBreakdown: OwnerBreakdown[];
  acquisitionTypeBreakdown: AcquisitionBreakdown[];
  
  // Contract Analytics
  contractDurationStats: ContractDurationStats;
  expiringContracts: ExpiringContract[];
  
  // Cost Savings and Optimization
  costSavingsAnalysis: CostSavingsAnalysis;
  earlyReturns: EarlyReturnAnalysis;
}

export interface CategoryBreakdown {
  categoria: string;
  count: number;
  percentage: number;
  totalCost: number;
  averageCost: number;
}

export interface GroupBreakdown {
  gruppo: string;
  count: number;
  percentage: number;
  totalCost: number;
  categories: string[];
}

export interface TransmissionBreakdown {
  trasmissione: string;
  count: number;
  percentage: number;
  avgCost: number;
}

export interface StatusBreakdown {
  stato: string;
  count: number;
  percentage: number;
  totalMonthlyCost: number;
}

export interface LocationBreakdown {
  location: string;
  vehicleCount: number;
  totalCost: number;
  categories: string[];
  utilizationRate: number;
}

export interface LocationSummary {
  location: string;
  vehicleCount: number;
  totalCost: number;
  rank: number;
}

export interface OwnerBreakdown {
  proprieta: string;
  vehicleCount: number;
  totalCost: number;
  percentage: number;
  avgCostPerVehicle: number;
}

export interface AcquisitionBreakdown {
  acquisizione: string;
  count: number;
  percentage: number;
  totalCost: number;
}

export interface ContractDurationStats {
  averageDuration: number;
  shortTermContracts: number; // <= 12 months
  mediumTermContracts: number; // 13-36 months
  longTermContracts: number; // > 36 months
}

export interface ExpiringContract {
  targa: string;
  modello: string;
  dataFine: Date;
  daysUntilExpiration: number;
  monthlyCost: number;
  location: string;
}

export interface CostSavingsAnalysis {
  totalSavings: number;
  totalSavingsWithVAT: number;
  totalDaysSaved: number;
  vehiclesWithEarlyReturns: number;
  averageSavingsPerVehicle: number;
  averageDaysSavedPerVehicle: number;
  potentialAnnualSavings: number;
}

export interface EarlyReturnAnalysis {
  earlyReturnDetails: EarlyReturnDetail[];
  topSavingVehicles: EarlyReturnDetail[];
  savingsByLocation: LocationSavings[];
  savingsByCategory: CategorySavings[];
}

export interface EarlyReturnDetail {
  targa: string;
  modello: string;
  categoria: string;
  location: string;
  dataFine: Date;
  dataEffettiva: Date;
  daysSaved: number;
  canoneImponibile: number;
  dailyCost: number;
  savingsPerVehicle: number;
}

export interface LocationSavings {
  location: string;
  totalSavings: number;
  vehicleCount: number;
  averageSavings: number;
}

export interface CategorySavings {
  categoria: string;
  totalSavings: number;
  vehicleCount: number;
  averageSavings: number;
}

// Advanced Filter Options
export interface AdvancedFilterOptions {
  // Basic Filters
  stato?: string[];
  categoria?: string[];
  gruppo?: string[];
  proprieta?: string[];
  parcoSede?: string[];
  
  // Vehicle Specifications
  tipologia?: string[];
  trasmissione?: string[];
  modello?: string[];
  acriss?: string[];
  
  // Contract Filters
  acquisizione?: string[];
  durataInMesi?: {
    min?: number;
    max?: number;
  };
  kmInclusi?: {
    min?: number;
    max?: number;
  };
  
  // Financial Filters
  canoneImponibile?: {
    min?: number;
    max?: number;
  };
  coperturaAssicurativa?: {
    min?: number;
    max?: number;
  };
  
  // Date Filters
  dateFilter?: DateFilterOptions;
  contractExpirationFilter?: {
    withinDays?: number;
    startDate?: Date;
    endDate?: Date;
  };
  
  // Advanced Options
  hasInsurance?: boolean;
  hasEarlyReturn?: boolean;
  hasNotes?: boolean;
}

export interface DateFilterOptions {
  dateField: 'DATA_INIZIO' | 'DATA_FINE' | 'DATA_EFFETTIVA' | 'RICONSEGNA';
  startDate: Date | null;
  endDate: Date | null;
}

// Data Visualization Interfaces
export interface ChartData {
  name: string;
  value: number;
  color?: string;
  percentage?: number;
}

export interface TimeSeriesData {
  date: string;
  value: number;
  category?: string;
}

export interface ComparisonData {
  name: string;
  current: number;
  previous: number;
  change: number;
  changePercentage: number;
}

// AI Chat Interfaces
export interface FleetInsightRequest {
  question: string;
  context: 'overview' | 'costs' | 'optimization' | 'maintenance' | 'contracts';
  fleetData?: FleetAnalytics;
}

export interface FleetInsightResponse {
  answer: string;
  insights: string[];
  recommendations: string[];
  dataReferences: string[];
  confidence: number;
}

export interface AICapability {
  id: string;
  name: string;
  description: string;
  category: 'analysis' | 'optimization' | 'prediction' | 'recommendation';
  enabled: boolean;
}

// Settings and Configuration
export interface FleetSettings {
  dataRefreshInterval: number; // minutes
  defaultCurrency: string;
  defaultDateFormat: string;
  enableAI: boolean;
  aiProvider: 'ollama' | 'local' | 'none';
  ollamaEndpoint?: string;
  ollamaModel?: string;
  enableNotifications: boolean;
  thresholds: {
    contractExpirationWarningDays: number;
    highCostAlert: number;
    lowUtilizationAlert: number;
  };
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'en' | 'it';
  favoriteCharts: string[];
  dashboardLayout: string;
  defaultFilters: AdvancedFilterOptions;
}