// Enhanced Fleet Data Service - Comprehensive analytics and data management
import <PERSON> from 'papaparse';
import { 
  FleetVehicle, 
  FleetAnalytics, 
  AdvancedFilterOptions,
  CategoryBreakdown,
  GroupBreakdown,
  LocationBreakdown,
  OwnerBreakdown,
  CostSavingsAnalysis,
  EarlyReturnAnalysis,
  ExpiringContract,
  ContractDurationStats,
  ChartData,
  TimeSeriesData
} from '@/types/fleet';

class EnhancedFleetDataService {
  private vehicles: FleetVehicle[] = [];
  private isLoaded = false;
  private lastLoadTime: Date | null = null;

  async loadData(csvContent?: string): Promise<void> {
    try {
      let content = csvContent;
      
      if (!content) {
        throw new Error('CSV content must be provided');
      }

      // Remove BOM if present
      if (content.charCodeAt(0) === 0xFEFF) {
        content = content.slice(1);
      }

      const parseResult = Papa.parse(content, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header) => header.trim(),
        encoding: 'UTF-8'
      });

      if (parseResult.errors.length > 0) {
        console.error('CSV parsing errors:', parseResult.errors);
      }

      this.vehicles = parseResult.data.map((row: any, index: number) => ({
        id: `vehicle_${index}`,
        // Vehicle Identification
        acriss: row['ACRISS'] || '',
        categoria: row['CATEGORIA'] || '',
        gruppo: row['GRUPPO'] || '',
        modello: row['MODELLO'] || '',
        tipologia: row['TIPOLOGIA'] || '',
        trasmissione: row['TRASMISSIONE'] || '',
        targa: row['TARGA'] || '',
        
        // Ownership and Contract
        proprieta: row['PROPRIETÀ'] || '',
        acquisizione: row['ACQUISIZIONE'] || '',
        durataInMesi: this.parseNumericValue(row['DURATA IN MESI']),
        kmInclusi: this.parseNumericValue(row['KM INCLUSI']),
        
        // Important Dates
        dataInizio: this.parseDate(row['DATA INIZIO']),
        dataFine: this.parseDate(row['DATA FINE']),
        riconsegna: this.parseDate(row['RICONSEGNA']),
        dataEffettiva: this.parseDate(row['DATA EFFETTIVA']),
        
        // Status and Location
        stato: row['STATO'] || '',
        parcoSede: row['PARCO/SEDE'] || '',
        
        // Financial Information
        canoneImponibile: this.parseMonetaryValue(row['CANONE IMPONIBILE']),
        canoneIvato: this.parseMonetaryValue(row['CANONE IVATO']),
        coperturaAssicurativa: this.parseMonetaryValue(row['COPERTURA ASS.']),
        
        // Additional Information
        note: row['NOTE'] || '',
        rifRiepilogo: row['RIF. RIEPILOGO'] || '',
        controlloCanoni: row['controllo CANONI IMPONIBILI'] || '',
        daVendere: row['DA VENDERE'] || '',
        rentToRent: row['RENT TO RENT'] || '',
      })).filter(vehicle => vehicle.targa); // Filter out empty rows

      this.isLoaded = true;
      this.lastLoadTime = new Date();
      console.log(`Enhanced Fleet Data Service: Loaded ${this.vehicles.length} vehicles from CSV`);
    } catch (error) {
      console.error('Error loading enhanced fleet data:', error);
      throw error;
    }
  }

  private parseDate(dateStr: string): Date | null {
    if (!dateStr || dateStr.trim() === '') return null;
    
    try {
      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? null : date;
    } catch {
      return null;
    }
  }

  private parseMonetaryValue(valueStr: string): number {
    if (!valueStr || valueStr.trim() === '') return 0;
    
    const cleanValue = valueStr
      .replace(/[€$,\s]/g, '')
      .replace(',', '.');
    
    const parsed = parseFloat(cleanValue);
    return isNaN(parsed) ? 0 : parsed;
  }

  private parseNumericValue(valueStr: string): number {
    if (!valueStr || valueStr.trim() === '') return 0;
    
    const cleanValue = valueStr.replace(/[,\s]/g, '');
    const parsed = parseFloat(cleanValue);
    return isNaN(parsed) ? 0 : parsed;
  }

  getVehicles(filters?: AdvancedFilterOptions): FleetVehicle[] {
    if (!this.isLoaded) {
      console.warn('Enhanced data not loaded yet');
      return [];
    }

    let filteredVehicles = [...this.vehicles];

    if (filters) {
      filteredVehicles = this.applyAdvancedFilters(filteredVehicles, filters);
    }

    return filteredVehicles;
  }

  private applyAdvancedFilters(vehicles: FleetVehicle[], filters: AdvancedFilterOptions): FleetVehicle[] {
    let filtered = vehicles;

    // Basic filters
    if (filters.stato?.length) {
      filtered = filtered.filter(v => filters.stato!.includes(v.stato));
    }
    if (filters.categoria?.length) {
      filtered = filtered.filter(v => filters.categoria!.includes(v.categoria));
    }
    if (filters.gruppo?.length) {
      filtered = filtered.filter(v => filters.gruppo!.includes(v.gruppo));
    }
    if (filters.proprieta?.length) {
      filtered = filtered.filter(v => filters.proprieta!.includes(v.proprieta));
    }
    if (filters.parcoSede?.length) {
      filtered = filtered.filter(v => filters.parcoSede!.includes(v.parcoSede));
    }

    // Vehicle specification filters
    if (filters.tipologia?.length) {
      filtered = filtered.filter(v => filters.tipologia!.includes(v.tipologia));
    }
    if (filters.trasmissione?.length) {
      filtered = filtered.filter(v => filters.trasmissione!.includes(v.trasmissione));
    }
    if (filters.modello?.length) {
      filtered = filtered.filter(v => filters.modello!.includes(v.modello));
    }
    if (filters.acriss?.length) {
      filtered = filtered.filter(v => filters.acriss!.includes(v.acriss));
    }
    if (filters.acquisizione?.length) {
      filtered = filtered.filter(v => filters.acquisizione!.includes(v.acquisizione));
    }

    // Numeric range filters
    if (filters.durataInMesi) {
      if (filters.durataInMesi.min !== undefined) {
        filtered = filtered.filter(v => v.durataInMesi >= filters.durataInMesi!.min!);
      }
      if (filters.durataInMesi.max !== undefined) {
        filtered = filtered.filter(v => v.durataInMesi <= filters.durataInMesi!.max!);
      }
    }

    if (filters.canoneImponibile) {
      if (filters.canoneImponibile.min !== undefined) {
        filtered = filtered.filter(v => v.canoneImponibile >= filters.canoneImponibile!.min!);
      }
      if (filters.canoneImponibile.max !== undefined) {
        filtered = filtered.filter(v => v.canoneImponibile <= filters.canoneImponibile!.max!);
      }
    }

    // Boolean filters
    if (filters.hasInsurance !== undefined) {
      filtered = filtered.filter(v => 
        filters.hasInsurance ? v.coperturaAssicurativa > 0 : v.coperturaAssicurativa === 0
      );
    }

    if (filters.hasEarlyReturn !== undefined) {
      filtered = filtered.filter(v => {
        const hasEarlyReturn = v.dataEffettiva && v.dataFine && v.dataEffettiva < v.dataFine;
        return filters.hasEarlyReturn ? hasEarlyReturn : !hasEarlyReturn;
      });
    }

    if (filters.hasNotes !== undefined) {
      filtered = filtered.filter(v => 
        filters.hasNotes ? v.note.trim() !== '' : v.note.trim() === ''
      );
    }

    // Date filters
    if (filters.dateFilter) {
      filtered = this.applyDateFilter(filtered, filters.dateFilter);
    }

    return filtered;
  }

  private applyDateFilter(vehicles: FleetVehicle[], dateFilter: any): FleetVehicle[] {
    const { dateField, startDate, endDate } = dateFilter;
    
    return vehicles.filter(vehicle => {
      let targetDate: Date | null = null;
      
      switch (dateField) {
        case 'DATA_INIZIO':
          targetDate = vehicle.dataInizio;
          break;
        case 'DATA_FINE':
          targetDate = vehicle.dataFine;
          break;
        case 'DATA_EFFETTIVA':
          targetDate = vehicle.dataEffettiva;
          break;
        case 'RICONSEGNA':
          targetDate = vehicle.riconsegna;
          break;
      }
      
      if (!targetDate) return true; // Include vehicles with null dates
      
      if (startDate && targetDate < startDate) return false;
      if (endDate && targetDate > endDate) return false;
      
      return true;
    });
  }

  calculateComprehensiveAnalytics(filters?: AdvancedFilterOptions): FleetAnalytics {
    const vehicles = this.getVehicles(filters);
    const activeVehicles = vehicles.filter(v => v.stato === 'Attivo');
    
    return {
      // Core Fleet Metrics
      totalVehicles: vehicles.length,
      activeVehicles: activeVehicles.length,
      utilizationRate: vehicles.length > 0 ? (activeVehicles.length / vehicles.length) * 100 : 0,
      
      // Financial Metrics
      totalMonthlyCost: activeVehicles.reduce((sum, v) => sum + v.canoneImponibile, 0),
      totalMonthlyCostWithVAT: activeVehicles.reduce((sum, v) => sum + v.canoneIvato, 0),
      averageCostPerVehicle: activeVehicles.length > 0 ? 
        activeVehicles.reduce((sum, v) => sum + v.canoneImponibile, 0) / activeVehicles.length : 0,
      totalInsuranceCoverage: vehicles.reduce((sum, v) => sum + v.coperturaAssicurativa, 0),
      
      // Fleet Composition
      categoryBreakdown: this.calculateCategoryBreakdown(vehicles),
      groupBreakdown: this.calculateGroupBreakdown(vehicles),
      transmissionBreakdown: this.calculateTransmissionBreakdown(vehicles),
      statusBreakdown: this.calculateStatusBreakdown(vehicles),
      
      // Location Analytics
      locationBreakdown: this.calculateLocationBreakdown(vehicles),
      topLocations: this.calculateTopLocations(vehicles),
      
      // Owner/Provider Analytics
      ownerBreakdown: this.calculateOwnerBreakdown(vehicles),
      acquisitionTypeBreakdown: this.calculateAcquisitionBreakdown(vehicles),
      
      // Contract Analytics
      contractDurationStats: this.calculateContractDurationStats(vehicles),
      expiringContracts: this.calculateExpiringContracts(vehicles),
      
      // Cost Savings and Optimization
      costSavingsAnalysis: this.calculateCostSavingsAnalysis(vehicles),
      earlyReturns: this.calculateEarlyReturnAnalysis(vehicles),
    };
  }

  private calculateCategoryBreakdown(vehicles: FleetVehicle[]): CategoryBreakdown[] {
    const categoryMap = new Map<string, {count: number, totalCost: number}>();
    
    vehicles.forEach(vehicle => {
      const existing = categoryMap.get(vehicle.categoria) || {count: 0, totalCost: 0};
      categoryMap.set(vehicle.categoria, {
        count: existing.count + 1,
        totalCost: existing.totalCost + vehicle.canoneImponibile
      });
    });

    return Array.from(categoryMap.entries()).map(([categoria, data]) => ({
      categoria,
      count: data.count,
      percentage: (data.count / vehicles.length) * 100,
      totalCost: data.totalCost,
      averageCost: data.count > 0 ? data.totalCost / data.count : 0
    })).sort((a, b) => b.count - a.count);
  }

  private calculateGroupBreakdown(vehicles: FleetVehicle[]): GroupBreakdown[] {
    const groupMap = new Map<string, {count: number, totalCost: number, categories: Set<string>}>();
    
    vehicles.forEach(vehicle => {
      const existing = groupMap.get(vehicle.gruppo) || {count: 0, totalCost: 0, categories: new Set()};
      existing.categories.add(vehicle.categoria);
      groupMap.set(vehicle.gruppo, {
        count: existing.count + 1,
        totalCost: existing.totalCost + vehicle.canoneImponibile,
        categories: existing.categories
      });
    });

    return Array.from(groupMap.entries()).map(([gruppo, data]) => ({
      gruppo,
      count: data.count,
      percentage: (data.count / vehicles.length) * 100,
      totalCost: data.totalCost,
      categories: Array.from(data.categories)
    })).sort((a, b) => b.count - a.count);
  }

  private calculateTransmissionBreakdown(vehicles: FleetVehicle[]) {
    const transmissionMap = new Map<string, {count: number, totalCost: number}>();
    
    vehicles.forEach(vehicle => {
      const existing = transmissionMap.get(vehicle.trasmissione) || {count: 0, totalCost: 0};
      transmissionMap.set(vehicle.trasmissione, {
        count: existing.count + 1,
        totalCost: existing.totalCost + vehicle.canoneImponibile
      });
    });

    return Array.from(transmissionMap.entries()).map(([trasmissione, data]) => ({
      trasmissione,
      count: data.count,
      percentage: (data.count / vehicles.length) * 100,
      avgCost: data.count > 0 ? data.totalCost / data.count : 0
    }));
  }

  private calculateStatusBreakdown(vehicles: FleetVehicle[]) {
    const statusMap = new Map<string, {count: number, totalCost: number}>();
    
    vehicles.forEach(vehicle => {
      const existing = statusMap.get(vehicle.stato) || {count: 0, totalCost: 0};
      statusMap.set(vehicle.stato, {
        count: existing.count + 1,
        totalCost: existing.totalCost + vehicle.canoneImponibile
      });
    });

    return Array.from(statusMap.entries()).map(([stato, data]) => ({
      stato,
      count: data.count,
      percentage: (data.count / vehicles.length) * 100,
      totalMonthlyCost: data.totalCost
    }));
  }

  private calculateLocationBreakdown(vehicles: FleetVehicle[]): LocationBreakdown[] {
    const locationMap = new Map<string, {
      count: number, 
      totalCost: number, 
      categories: Set<string>,
      activeCount: number
    }>();
    
    vehicles.forEach(vehicle => {
      const existing = locationMap.get(vehicle.parcoSede) || {
        count: 0, 
        totalCost: 0, 
        categories: new Set(),
        activeCount: 0
      };
      existing.categories.add(vehicle.categoria);
      if (vehicle.stato === 'Attivo') existing.activeCount++;
      
      locationMap.set(vehicle.parcoSede, {
        count: existing.count + 1,
        totalCost: existing.totalCost + vehicle.canoneImponibile,
        categories: existing.categories,
        activeCount: existing.activeCount
      });
    });

    return Array.from(locationMap.entries()).map(([location, data]) => ({
      location,
      vehicleCount: data.count,
      totalCost: data.totalCost,
      categories: Array.from(data.categories),
      utilizationRate: data.count > 0 ? (data.activeCount / data.count) * 100 : 0
    })).sort((a, b) => b.vehicleCount - a.vehicleCount);
  }

  private calculateTopLocations(vehicles: FleetVehicle[]) {
    const locationBreakdown = this.calculateLocationBreakdown(vehicles);
    return locationBreakdown.slice(0, 10).map((location, index) => ({
      location: location.location,
      vehicleCount: location.vehicleCount,
      totalCost: location.totalCost,
      rank: index + 1
    }));
  }

  private calculateOwnerBreakdown(vehicles: FleetVehicle[]): OwnerBreakdown[] {
    const ownerMap = new Map<string, {count: number, totalCost: number}>();
    
    vehicles.forEach(vehicle => {
      const existing = ownerMap.get(vehicle.proprieta) || {count: 0, totalCost: 0};
      ownerMap.set(vehicle.proprieta, {
        count: existing.count + 1,
        totalCost: existing.totalCost + vehicle.canoneImponibile
      });
    });

    return Array.from(ownerMap.entries()).map(([proprieta, data]) => ({
      proprieta,
      vehicleCount: data.count,
      totalCost: data.totalCost,
      percentage: (data.count / vehicles.length) * 100,
      avgCostPerVehicle: data.count > 0 ? data.totalCost / data.count : 0
    })).sort((a, b) => b.vehicleCount - a.vehicleCount);
  }

  private calculateAcquisitionBreakdown(vehicles: FleetVehicle[]) {
    const acquisitionMap = new Map<string, {count: number, totalCost: number}>();
    
    vehicles.forEach(vehicle => {
      const existing = acquisitionMap.get(vehicle.acquisizione) || {count: 0, totalCost: 0};
      acquisitionMap.set(vehicle.acquisizione, {
        count: existing.count + 1,
        totalCost: existing.totalCost + vehicle.canoneImponibile
      });
    });

    return Array.from(acquisitionMap.entries()).map(([acquisizione, data]) => ({
      acquisizione,
      count: data.count,
      percentage: (data.count / vehicles.length) * 100,
      totalCost: data.totalCost
    }));
  }

  private calculateContractDurationStats(vehicles: FleetVehicle[]): ContractDurationStats {
    const validDurations = vehicles
      .map(v => v.durataInMesi)
      .filter(duration => duration > 0);

    const shortTerm = validDurations.filter(d => d <= 12).length;
    const mediumTerm = validDurations.filter(d => d > 12 && d <= 36).length;
    const longTerm = validDurations.filter(d => d > 36).length;

    return {
      averageDuration: validDurations.length > 0 ? 
        validDurations.reduce((sum, d) => sum + d, 0) / validDurations.length : 0,
      shortTermContracts: shortTerm,
      mediumTermContracts: mediumTerm,
      longTermContracts: longTerm
    };
  }

  private calculateExpiringContracts(vehicles: FleetVehicle[]): ExpiringContract[] {
    const today = new Date();
    const thirtyDaysFromNow = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);

    return vehicles
      .filter(v => v.dataFine && v.dataFine >= today && v.dataFine <= thirtyDaysFromNow)
      .map(vehicle => ({
        targa: vehicle.targa,
        modello: vehicle.modello,
        dataFine: vehicle.dataFine!,
        daysUntilExpiration: Math.ceil((vehicle.dataFine!.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)),
        monthlyCost: vehicle.canoneImponibile,
        location: vehicle.parcoSede
      }))
      .sort((a, b) => a.daysUntilExpiration - b.daysUntilExpiration);
  }

  private calculateCostSavingsAnalysis(vehicles: FleetVehicle[]): CostSavingsAnalysis {
    const activeVehicles = vehicles.filter(v => 
      v.stato === 'Attivo' && 
      v.dataEffettiva && 
      v.dataFine
    );

    const earlyReturns = activeVehicles.filter(v => 
      v.dataEffettiva! < v.dataFine!
    );

    const totalSavings = earlyReturns.reduce((sum, vehicle) => {
      const daysSaved = Math.floor(
        (vehicle.dataFine!.getTime() - vehicle.dataEffettiva!.getTime()) / (1000 * 60 * 60 * 24)
      );
      const dailyCost = vehicle.canoneImponibile / 30.44;
      return sum + (daysSaved * dailyCost);
    }, 0);

    const totalDaysSaved = earlyReturns.reduce((sum, vehicle) => {
      return sum + Math.floor(
        (vehicle.dataFine!.getTime() - vehicle.dataEffettiva!.getTime()) / (1000 * 60 * 60 * 24)
      );
    }, 0);

    return {
      totalSavings,
      totalSavingsWithVAT: totalSavings * 1.22,
      totalDaysSaved,
      vehiclesWithEarlyReturns: earlyReturns.length,
      averageSavingsPerVehicle: earlyReturns.length > 0 ? totalSavings / earlyReturns.length : 0,
      averageDaysSavedPerVehicle: earlyReturns.length > 0 ? totalDaysSaved / earlyReturns.length : 0,
      potentialAnnualSavings: totalSavings * 12
    };
  }

  private calculateEarlyReturnAnalysis(vehicles: FleetVehicle[]): EarlyReturnAnalysis {
    const activeVehicles = vehicles.filter(v => 
      v.stato === 'Attivo' && 
      v.dataEffettiva && 
      v.dataFine
    );

    const earlyReturns = activeVehicles.filter(v => 
      v.dataEffettiva! < v.dataFine!
    );

    const earlyReturnDetails = earlyReturns.map(vehicle => {
      const daysSaved = Math.floor(
        (vehicle.dataFine!.getTime() - vehicle.dataEffettiva!.getTime()) / (1000 * 60 * 60 * 24)
      );
      const dailyCost = vehicle.canoneImponibile / 30.44;
      const savingsPerVehicle = daysSaved * dailyCost;

      return {
        targa: vehicle.targa,
        modello: vehicle.modello,
        categoria: vehicle.categoria,
        location: vehicle.parcoSede,
        dataFine: vehicle.dataFine!,
        dataEffettiva: vehicle.dataEffettiva!,
        daysSaved,
        canoneImponibile: vehicle.canoneImponibile,
        dailyCost,
        savingsPerVehicle,
      };
    });

    return {
      earlyReturnDetails,
      topSavingVehicles: earlyReturnDetails
        .sort((a, b) => b.savingsPerVehicle - a.savingsPerVehicle)
        .slice(0, 10),
      savingsByLocation: this.calculateSavingsByLocation(earlyReturnDetails),
      savingsByCategory: this.calculateSavingsByCategory(earlyReturnDetails)
    };
  }

  private calculateSavingsByLocation(earlyReturns: any[]) {
    const locationMap = new Map<string, {totalSavings: number, count: number}>();
    
    earlyReturns.forEach(vehicle => {
      const existing = locationMap.get(vehicle.location) || {totalSavings: 0, count: 0};
      locationMap.set(vehicle.location, {
        totalSavings: existing.totalSavings + vehicle.savingsPerVehicle,
        count: existing.count + 1
      });
    });

    return Array.from(locationMap.entries()).map(([location, data]) => ({
      location,
      totalSavings: data.totalSavings,
      vehicleCount: data.count,
      averageSavings: data.totalSavings / data.count
    })).sort((a, b) => b.totalSavings - a.totalSavings);
  }

  private calculateSavingsByCategory(earlyReturns: any[]) {
    const categoryMap = new Map<string, {totalSavings: number, count: number}>();
    
    earlyReturns.forEach(vehicle => {
      const existing = categoryMap.get(vehicle.categoria) || {totalSavings: 0, count: 0};
      categoryMap.set(vehicle.categoria, {
        totalSavings: existing.totalSavings + vehicle.savingsPerVehicle,
        count: existing.count + 1
      });
    });

    return Array.from(categoryMap.entries()).map(([categoria, data]) => ({
      categoria,
      totalSavings: data.totalSavings,
      vehicleCount: data.count,
      averageSavings: data.totalSavings / data.count
    })).sort((a, b) => b.totalSavings - a.totalSavings);
  }

  // Utility methods for getting unique values
  getUniqueValues(field: keyof FleetVehicle): string[] {
    if (!this.isLoaded) return [];
    
    const values = this.vehicles
      .map(vehicle => vehicle[field] as string)
      .filter((value, index, array) => 
        value !== null && 
        value !== undefined && 
        value !== '' && 
        array.indexOf(value) === index
      );
    
    return values.sort();
  }

  // Data export functionality
  exportToCSV(filters?: AdvancedFilterOptions): string {
    const vehicles = this.getVehicles(filters);
    
    const csvData = vehicles.map(vehicle => ({
      TARGA: vehicle.targa,
      MODELLO: vehicle.modello,
      CATEGORIA: vehicle.categoria,
      GRUPPO: vehicle.gruppo,
      TIPOLOGIA: vehicle.tipologia,
      TRASMISSIONE: vehicle.trasmissione,
      STATO: vehicle.stato,
      'PROPRIETÀ': vehicle.proprieta,
      ACQUISIZIONE: vehicle.acquisizione,
      'DURATA IN MESI': vehicle.durataInMesi,
      'KM INCLUSI': vehicle.kmInclusi,
      'PARCO/SEDE': vehicle.parcoSede,
      'DATA INIZIO': vehicle.dataInizio?.toLocaleDateString('it-IT'),
      'DATA FINE': vehicle.dataFine?.toLocaleDateString('it-IT'),
      'DATA EFFETTIVA': vehicle.dataEffettiva?.toLocaleDateString('it-IT'),
      RICONSEGNA: vehicle.riconsegna?.toLocaleDateString('it-IT'),
      'CANONE IMPONIBILE': vehicle.canoneImponibile,
      'CANONE IVATO': vehicle.canoneIvato,
      'COPERTURA ASS.': vehicle.coperturaAssicurativa,
      NOTE: vehicle.note,
    }));

    return Papa.unparse(csvData);
  }

  // Statistics and insights for AI
  getFleetInsights(filters?: AdvancedFilterOptions): any {
    const analytics = this.calculateComprehensiveAnalytics(filters);
    
    return {
      summary: {
        totalVehicles: analytics.totalVehicles,
        utilizationRate: analytics.utilizationRate,
        totalMonthlyCost: analytics.totalMonthlyCost,
        costSavings: analytics.costSavingsAnalysis.totalSavings
      },
      trends: {
        topCategories: analytics.categoryBreakdown.slice(0, 5),
        topLocations: analytics.topLocations.slice(0, 5),
        expiringContracts: analytics.expiringContracts.length
      },
      optimizationOpportunities: {
        earlyReturnSavings: analytics.costSavingsAnalysis,
        underutilizedLocations: analytics.locationBreakdown
          .filter(loc => loc.utilizationRate < 80)
          .slice(0, 5),
        highCostVehicles: analytics.categoryBreakdown
          .filter(cat => cat.averageCost > analytics.averageCostPerVehicle * 1.2)
      }
    };
  }

  // Utility: Group vehicles by month (using dataEffettiva or dataFine)
  private groupByMonth(vehicles: FleetVehicle[], dateField: keyof FleetVehicle) {
    const groups: Record<string, FleetVehicle[]> = {};
    vehicles.forEach(vehicle => {
      const date: Date | undefined = vehicle[dateField] as Date | undefined;
      if (!date) return;
      const monthKey = `${date.getFullYear()}-${(date.getMonth()+1).toString().padStart(2, '0')}`;
      if (!groups[monthKey]) groups[monthKey] = [];
      groups[monthKey].push(vehicle);
    });
    return groups;
  }

  // New: Monthly Cost Savings Breakdown
  public getMonthlyCostSavings(filters?: AdvancedFilterOptions) {
    const vehicles = this.getVehicles(filters);
    const activeVehicles = vehicles.filter(v => v.stato === 'Attivo' && v.dataEffettiva && v.dataFine);
    const earlyReturns = activeVehicles.filter(v => v.dataEffettiva! < v.dataFine!);
    const grouped = this.groupByMonth(earlyReturns, 'dataEffettiva');
    const result = Object.entries(grouped).map(([month, vehicles]) => {
      let totalSavings = 0, totalSavingsWithVAT = 0, totalDaysSaved = 0;
      const byCategory: Record<string, number> = {};
      const byLocation: Record<string, number> = {};
      vehicles.forEach(vehicle => {
        const daysSaved = Math.floor((vehicle.dataFine!.getTime() - vehicle.dataEffettiva!.getTime()) / (1000 * 60 * 60 * 24));
        const dailyCost = vehicle.canoneImponibile / 30.44;
        const savings = daysSaved * dailyCost;
        totalSavings += savings;
        totalSavingsWithVAT += savings * 1.22;
        totalDaysSaved += daysSaved;
        if (vehicle.categoria) byCategory[vehicle.categoria] = (byCategory[vehicle.categoria] || 0) + savings;
        if (vehicle.parcoSede) byLocation[vehicle.parcoSede] = (byLocation[vehicle.parcoSede] || 0) + savings;
      });
      return { month, totalSavings, totalSavingsWithVAT, totalDaysSaved, byCategory, byLocation, vehicleCount: vehicles.length };
    });
    return result.sort((a, b) => a.month.localeCompare(b.month));
  }

  // New: Monthly Cost Analysis Breakdown
  public getMonthlyCostAnalysis(filters?: AdvancedFilterOptions) {
    const vehicles = this.getVehicles(filters);
    const activeVehicles = vehicles.filter(v => v.stato === 'Attivo' && v.dataEffettiva && v.dataFine);
    const grouped = this.groupByMonth(activeVehicles, 'dataEffettiva');
    const result = Object.entries(grouped).map(([month, vehicles]) => {
      let totalCost = 0, totalCostWithVAT = 0;
      const byCategory: Record<string, number> = {};
      const byLocation: Record<string, number> = {};
      const byType: Record<string, number> = {};
      vehicles.forEach(vehicle => {
        totalCost += vehicle.canoneImponibile;
        totalCostWithVAT += vehicle.canoneIvato;
        if (vehicle.categoria) byCategory[vehicle.categoria] = (byCategory[vehicle.categoria] || 0) + vehicle.canoneImponibile;
        if (vehicle.parcoSede) byLocation[vehicle.parcoSede] = (byLocation[vehicle.parcoSede] || 0) + vehicle.canoneImponibile;
        if (vehicle.tipologia) byType[vehicle.tipologia] = (byType[vehicle.tipologia] || 0) + vehicle.canoneImponibile;
      });
      return { month, totalCost, totalCostWithVAT, byCategory, byLocation, byType, vehicleCount: vehicles.length };
    });
    return result.sort((a, b) => a.month.localeCompare(b.month));
  }
}

export const enhancedFleetDataService = new EnhancedFleetDataService();
export default enhancedFleetDataService;