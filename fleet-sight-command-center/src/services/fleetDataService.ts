// Fleet Data Service - Integration with our CSV data
// Replaces mockDataService with real fleet management data

import Papa from 'papaparse';

export interface FleetVehicle {
  id: string;
  targa: string;
  modello: string;
  categoria: string;
  gruppo: string;
  stato: string;
  proprieta: string;
  parcoSede: string;
  dataInizio: Date;
  dataFine: Date;
  dataEffettiva: Date | null;
  riconsegna: Date | null;
  canoneImponibile: number;
  canoneIvato: number;
  assicurazione: string;
}

export interface FleetKPIs {
  totalVehicles: number;
  activeVehicles: number;
  totalMonthlyCost: number;
  totalMonthlyCostWithVAT: number;
  averageCostPerVehicle: number;
  utilizationRate: number;
  vehiclesWithInsurance: number;
  insuranceRate: number;
}

export interface CostSavingsAnalysis {
  totalSavings: number;
  totalSavingsWithVAT: number;
  totalDaysSaved: number;
  vehiclesWithEarlyReturns: number;
  averageSavingsPerVehicle: number;
  averageDaysSavedPerVehicle: number;
  earlyReturnDetails: EarlyReturnDetail[];
}

export interface EarlyReturnDetail {
  targa: string;
  modello: string;
  dataFine: Date;
  dataEffettiva: Date;
  daysSaved: number;
  canoneImponibile: number;
  dailyCost: number;
  savingsPerVehicle: number;
}

export interface ExpirationAnalysis {
  nextMonthExpirations: number;
  nextMonthCostReduction: number;
  nextMonthCostReductionWithVAT: number;
  q1Expirations: number;
  q1CostImpact: number;
  q1CostImpactWithVAT: number;
  annualSavingsFromExpirations: number;
  annualSavingsFromExpirationsWithVAT: number;
}

export interface DateFilterOptions {
  dateField: 'DATA_FINE' | 'DATA_EFFETTIVA';
  startDate: Date | null;
  endDate: Date | null;
}

export interface FilterOptions {
  stato?: string[];
  categoria?: string[];
  gruppo?: string[];
  proprieta?: string[];
  parcoSede?: string[];
  assicurazione?: string[];
  dateFilter?: DateFilterOptions;
}

class FleetDataService {
  private vehicles: FleetVehicle[] = [];
  private isLoaded = false;

  async loadData(csvContent?: string): Promise<void> {
    try {
      let content = csvContent;
      
      if (!content) {
        // In a real implementation, you would fetch from your data source
        // For now, we'll assume the CSV content is provided
        throw new Error('CSV content must be provided');
      }

      const parseResult = Papa.parse(content, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header) => header.trim(),
      });

      if (parseResult.errors.length > 0) {
        console.error('CSV parsing errors:', parseResult.errors);
      }

      this.vehicles = parseResult.data.map((row: any, index: number) => ({
        id: `vehicle_${index}`,
        targa: row['TARGA'] || '',
        modello: row['MODELLO'] || '',
        categoria: row['CATEGORIA'] || '',
        gruppo: row['GRUPPO'] || '',
        stato: row['STATO'] || '',
        proprieta: row['PROPRIETÀ'] || '',
        parcoSede: row['PARCO/SEDE'] || '',
        dataInizio: this.parseDate(row['DATA INIZIO']),
        dataFine: this.parseDate(row['DATA FINE']),
        dataEffettiva: this.parseDate(row['DATA EFFETTIVA']),
        riconsegna: this.parseDate(row['RICONSEGNA']),
        canoneImponibile: parseFloat(row['CANONE IMPONIBILE']) || 0,
        canoneIvato: parseFloat(row['CANONE IVATO']) || 0,
        assicurazione: row['ASSICURAZIONE'] || '',
      })).filter(vehicle => vehicle.targa); // Filter out empty rows

      this.isLoaded = true;
      console.log(`Loaded ${this.vehicles.length} vehicles from CSV`);
    } catch (error) {
      console.error('Error loading fleet data:', error);
      throw error;
    }
  }

  private parseDate(dateStr: string): Date | null {
    if (!dateStr || dateStr.trim() === '') return null;
    
    // Handle MM/DD/YY format
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  }

  getVehicles(filters?: FilterOptions): FleetVehicle[] {
    if (!this.isLoaded) {
      console.warn('Data not loaded yet');
      return [];
    }

    let filteredVehicles = [...this.vehicles];

    if (filters) {
      // Apply status filter
      if (filters.stato && filters.stato.length > 0) {
        filteredVehicles = filteredVehicles.filter(v => 
          filters.stato!.includes(v.stato)
        );
      }

      // Apply category filter
      if (filters.categoria && filters.categoria.length > 0) {
        filteredVehicles = filteredVehicles.filter(v => 
          filters.categoria!.includes(v.categoria)
        );
      }

      // Apply group filter
      if (filters.gruppo && filters.gruppo.length > 0) {
        filteredVehicles = filteredVehicles.filter(v => 
          filters.gruppo!.includes(v.gruppo)
        );
      }

      // Apply property filter
      if (filters.proprieta && filters.proprieta.length > 0) {
        filteredVehicles = filteredVehicles.filter(v => 
          filters.proprieta!.includes(v.proprieta)
        );
      }

      // Apply location filter
      if (filters.parcoSede && filters.parcoSede.length > 0) {
        filteredVehicles = filteredVehicles.filter(v => 
          filters.parcoSede!.includes(v.parcoSede)
        );
      }

      // Apply insurance filter
      if (filters.assicurazione && filters.assicurazione.length > 0) {
        filteredVehicles = filteredVehicles.filter(v => 
          filters.assicurazione!.includes(v.assicurazione)
        );
      }

      // Apply date filter
      if (filters.dateFilter) {
        const { dateField, startDate, endDate } = filters.dateFilter;
        
        filteredVehicles = filteredVehicles.filter(vehicle => {
          const targetDate = dateField === 'DATA_FINE' ? vehicle.dataFine : vehicle.dataEffettiva;
          
          if (!targetDate) return true; // Include vehicles with null dates
          
          if (startDate && targetDate < startDate) return false;
          if (endDate && targetDate > endDate) return false;
          
          return true;
        });
      }
    }

    return filteredVehicles;
  }

  calculateKPIs(filters?: FilterOptions): FleetKPIs {
    const vehicles = this.getVehicles(filters);
    const activeVehicles = vehicles.filter(v => v.stato === 'Attivo');
    
    const totalMonthlyCost = activeVehicles.reduce((sum, v) => sum + v.canoneImponibile, 0);
    const totalMonthlyCostWithVAT = activeVehicles.reduce((sum, v) => sum + v.canoneIvato, 0);
    const vehiclesWithInsurance = vehicles.filter(v => v.assicurazione && v.assicurazione !== '').length;

    return {
      totalVehicles: vehicles.length,
      activeVehicles: activeVehicles.length,
      totalMonthlyCost,
      totalMonthlyCostWithVAT,
      averageCostPerVehicle: activeVehicles.length > 0 ? totalMonthlyCost / activeVehicles.length : 0,
      utilizationRate: vehicles.length > 0 ? (activeVehicles.length / vehicles.length) * 100 : 0,
      vehiclesWithInsurance,
      insuranceRate: vehicles.length > 0 ? (vehiclesWithInsurance / vehicles.length) * 100 : 0,
    };
  }

  calculateCostSavings(filters?: FilterOptions): CostSavingsAnalysis {
    const vehicles = this.getVehicles(filters);
    const activeVehicles = vehicles.filter(v => 
      v.stato === 'Attivo' && 
      v.dataEffettiva && 
      v.dataFine
    );

    // Find early returns (delivery date < end date)
    const earlyReturns = activeVehicles.filter(v => 
      v.dataEffettiva! < v.dataFine
    );

    const earlyReturnDetails: EarlyReturnDetail[] = earlyReturns.map(vehicle => {
      const daysSaved = Math.floor(
        (vehicle.dataFine.getTime() - vehicle.dataEffettiva!.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      // Calculate daily cost (monthly cost / average days in month)
      const avgDaysPerMonth = 30.44;
      const dailyCost = vehicle.canoneImponibile / avgDaysPerMonth;
      const savingsPerVehicle = daysSaved * dailyCost;

      return {
        targa: vehicle.targa,
        modello: vehicle.modello,
        dataFine: vehicle.dataFine,
        dataEffettiva: vehicle.dataEffettiva!,
        daysSaved,
        canoneImponibile: vehicle.canoneImponibile,
        dailyCost,
        savingsPerVehicle,
      };
    });

    const totalSavings = earlyReturnDetails.reduce((sum, detail) => sum + detail.savingsPerVehicle, 0);
    const totalSavingsWithVAT = totalSavings * 1.22; // Assuming 22% VAT
    const totalDaysSaved = earlyReturnDetails.reduce((sum, detail) => sum + detail.daysSaved, 0);

    return {
      totalSavings,
      totalSavingsWithVAT,
      totalDaysSaved,
      vehiclesWithEarlyReturns: earlyReturns.length,
      averageSavingsPerVehicle: earlyReturns.length > 0 ? totalSavings / earlyReturns.length : 0,
      averageDaysSavedPerVehicle: earlyReturns.length > 0 ? totalDaysSaved / earlyReturns.length : 0,
      earlyReturnDetails,
    };
  }

  getUniqueValues(field: keyof FleetVehicle): string[] {
    const values = this.vehicles
      .map(vehicle => vehicle[field])
      .filter((value, index, array) => 
        value !== null && 
        value !== undefined && 
        value !== '' && 
        array.indexOf(value) === index
      ) as string[];
    
    return values.sort();
  }

  exportToCSV(filters?: FilterOptions): string {
    const vehicles = this.getVehicles(filters);
    
    const csvData = vehicles.map(vehicle => ({
      TARGA: vehicle.targa,
      MODELLO: vehicle.modello,
      CATEGORIA: vehicle.categoria,
      GRUPPO: vehicle.gruppo,
      STATO: vehicle.stato,
      'PROPRIETÀ': vehicle.proprieta,
      'PARCO/SEDE': vehicle.parcoSede,
      'DATA INIZIO': vehicle.dataInizio?.toLocaleDateString('it-IT'),
      'DATA FINE': vehicle.dataFine?.toLocaleDateString('it-IT'),
      'DATA EFFETTIVA': vehicle.dataEffettiva?.toLocaleDateString('it-IT'),
      RICONSEGNA: vehicle.riconsegna?.toLocaleDateString('it-IT'),
      'CANONE IMPONIBILE': vehicle.canoneImponibile,
      'CANONE IVATO': vehicle.canoneIvato,
      ASSICURAZIONE: vehicle.assicurazione,
    }));

    return Papa.unparse(csvData);
  }

  // Utility: Group vehicles by month (using dataEffettiva or dataFine)
  private groupByMonth(vehicles: FleetVehicle[], dateField: keyof FleetVehicle) {
    const groups: Record<string, FleetVehicle[]> = {};
    vehicles.forEach(vehicle => {
      const date: Date | undefined = vehicle[dateField] as Date | undefined;
      if (!date) return;
      const monthKey = `${date.getFullYear()}-${(date.getMonth()+1).toString().padStart(2, '0')}`;
      if (!groups[monthKey]) groups[monthKey] = [];
      groups[monthKey].push(vehicle);
    });
    return groups;
  }

  // New: Monthly Cost Savings Breakdown
  public getMonthlyCostSavings(filters?: FilterOptions) {
    const vehicles = this.getVehicles(filters);
    const activeVehicles = vehicles.filter(v => v.stato === 'Attivo' && v.dataEffettiva && v.dataFine);
    const earlyReturns = activeVehicles.filter(v => v.dataEffettiva! < v.dataFine!);
    const grouped = this.groupByMonth(earlyReturns, 'dataEffettiva');
    const result = Object.entries(grouped).map(([month, vehicles]) => {
      let totalSavings = 0, totalSavingsWithVAT = 0, totalDaysSaved = 0;
      const byCategory: Record<string, number> = {};
      const byLocation: Record<string, number> = {};
      vehicles.forEach(vehicle => {
        const daysSaved = Math.floor((vehicle.dataFine!.getTime() - vehicle.dataEffettiva!.getTime()) / (1000 * 60 * 60 * 24));
        const dailyCost = vehicle.canoneImponibile / 30.44;
        const savings = daysSaved * dailyCost;
        totalSavings += savings;
        totalSavingsWithVAT += savings * 1.22;
        totalDaysSaved += daysSaved;
        if (vehicle.categoria) byCategory[vehicle.categoria] = (byCategory[vehicle.categoria] || 0) + savings;
        if (vehicle.parcoSede) byLocation[vehicle.parcoSede] = (byLocation[vehicle.parcoSede] || 0) + savings;
      });
      return { month, totalSavings, totalSavingsWithVAT, totalDaysSaved, byCategory, byLocation, vehicleCount: vehicles.length };
    });
    return result.sort((a, b) => a.month.localeCompare(b.month));
  }

  // New: Monthly Cost Analysis Breakdown
  public getMonthlyCostAnalysis(filters?: FilterOptions) {
    const vehicles = this.getVehicles(filters);
    const activeVehicles = vehicles.filter(v => v.stato === 'Attivo' && v.dataEffettiva && v.dataFine);
    const grouped = this.groupByMonth(activeVehicles, 'dataEffettiva');
    const result = Object.entries(grouped).map(([month, vehicles]) => {
      let totalCost = 0, totalCostWithVAT = 0;
      const byCategory: Record<string, number> = {};
      const byLocation: Record<string, number> = {};
      const byType: Record<string, number> = {};
      vehicles.forEach(vehicle => {
        totalCost += vehicle.canoneImponibile;
        totalCostWithVAT += vehicle.canoneIvato;
        if (vehicle.categoria) byCategory[vehicle.categoria] = (byCategory[vehicle.categoria] || 0) + vehicle.canoneImponibile;
        if (vehicle.parcoSede) byLocation[vehicle.parcoSede] = (byLocation[vehicle.parcoSede] || 0) + vehicle.canoneImponibile;
        if (vehicle.tipologia) byType[vehicle.tipologia] = (byType[vehicle.tipologia] || 0) + vehicle.canoneImponibile;
      });
      return { month, totalCost, totalCostWithVAT, byCategory, byLocation, byType, vehicleCount: vehicles.length };
    });
    return result.sort((a, b) => a.month.localeCompare(b.month));
  }
}

// Export singleton instance
export const fleetDataService = new FleetDataService();
export default fleetDataService;
