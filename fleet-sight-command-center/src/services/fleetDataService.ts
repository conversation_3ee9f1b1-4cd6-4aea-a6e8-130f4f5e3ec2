// Fleet Data Service - Integration with our CSV data
// Replaces mockDataService with real fleet management data

import Papa from 'papaparse';

export interface FleetVehicle {
  id: string;
  targa: string;
  modello: string;
  categoria: string;
  gruppo: string;
  stato: string;
  proprieta: string;
  parcoSede: string;
  dataInizio: Date;
  dataFine: Date;
  dataEffettiva: Date | null;
  riconsegna: Date | null;
  canoneImponibile: number;
  canoneIvato: number;
  assicurazione: string;
}

export interface FleetKPIs {
  totalVehicles: number;
  activeVehicles: number;
  totalMonthlyCost: number;
  totalMonthlyCostWithVAT: number;
  averageCostPerVehicle: number;
  utilizationRate: number;
  vehiclesWithInsurance: number;
  insuranceRate: number;
}

export interface CostSavingsAnalysis {
  totalSavings: number;
  totalSavingsWithVAT: number;
  totalDaysSaved: number;
  vehiclesWithEarlyReturns: number;
  averageSavingsPerVehicle: number;
  averageDaysSavedPerVehicle: number;
  earlyReturnDetails: EarlyReturnDetail[];
}

export interface EarlyReturnDetail {
  targa: string;
  modello: string;
  dataFine: Date;
  dataEffettiva: Date;
  daysSaved: number;
  canoneImponibile: number;
  dailyCost: number;
  savingsPerVehicle: number;
}

export interface ExpirationAnalysis {
  nextMonthExpirations: number;
  nextMonthCostReduction: number;
  nextMonthCostReductionWithVAT: number;
  q1Expirations: number;
  q1CostImpact: number;
  q1CostImpactWithVAT: number;
  annualSavingsFromExpirations: number;
  annualSavingsFromExpirationsWithVAT: number;
}

export interface DateFilterOptions {
  dateField: 'DATA_FINE' | 'DATA_EFFETTIVA';
  startDate: Date | null;
  endDate: Date | null;
}

export interface FilterOptions {
  stato?: string[];
  categoria?: string[];
  gruppo?: string[];
  proprieta?: string[];
  parcoSede?: string[];
  assicurazione?: string[];
  dateFilter?: DateFilterOptions;
}

class FleetDataService {
  private vehicles: FleetVehicle[] = [];
  private isLoaded = false;

  async loadData(csvContent?: string): Promise<void> {
    try {
      let content = csvContent;
      
      if (!content) {
        // In a real implementation, you would fetch from your data source
        // For now, we'll assume the CSV content is provided
        throw new Error('CSV content must be provided');
      }

      // Remove BOM if present
      if (content.charCodeAt(0) === 0xFEFF) {
        content = content.slice(1);
      }

      const parseResult = Papa.parse(content, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header) => header.trim(),
        encoding: 'UTF-8'
      });

      if (parseResult.errors.length > 0) {
        console.error('CSV parsing errors:', parseResult.errors);
      }

      this.vehicles = parseResult.data.map((row: any, index: number) => ({
        id: `vehicle_${index}`,
        targa: row['TARGA'] || '',
        modello: row['MODELLO'] || '',
        categoria: row['CATEGORIA'] || '',
        gruppo: row['GRUPPO'] || '',
        stato: row['STATO'] || '',
        proprieta: row['PROPRIETÀ'] || '',
        parcoSede: row['PARCO/SEDE'] || '',
        dataInizio: this.parseDate(row['DATA INIZIO']),
        dataFine: this.parseDate(row['DATA FINE']),
        dataEffettiva: this.parseDate(row['DATA EFFETTIVA']),
        riconsegna: this.parseDate(row['RICONSEGNA']),
        canoneImponibile: this.parseMonetaryValue(row['CANONE IMPONIBILE']),
        canoneIvato: this.parseMonetaryValue(row['CANONE IVATO']),
        assicurazione: row['COPERTURA ASS.'] || '',
      })).filter(vehicle => vehicle.targa); // Filter out empty rows

      this.isLoaded = true;
      console.log(`Loaded ${this.vehicles.length} vehicles from CSV`);
    } catch (error) {
      console.error('Error loading fleet data:', error);
      throw error;
    }
  }

  private parseDate(dateStr: string): Date | null {
    if (!dateStr || dateStr.trim() === '') return null;
    
    try {
      // Handle MM/DD/YY format
      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? null : date;
    } catch {
      return null;
    }
  }

  private parseMonetaryValue(valueStr: string): number {
    if (!valueStr || valueStr.trim() === '') return 0;
    
    // Remove currency symbols, commas, and spaces
    const cleanValue = valueStr
      .replace(/[€$,\s]/g, '')
      .replace(',', '.');
    
    const parsed = parseFloat(cleanValue);
    return isNaN(parsed) ? 0 : parsed;
  }

  getVehicles(filters?: FilterOptions): FleetVehicle[] {
    if (!this.isLoaded) {
      console.warn('Data not loaded yet');
      return [];
    }

    let filteredVehicles = [...this.vehicles];

    if (filters) {
      // Apply status filter
      if (filters.stato && filters.stato.length > 0) {
        filteredVehicles = filteredVehicles.filter(v => 
          filters.stato!.includes(v.stato)
        );
      }

      // Apply category filter
      if (filters.categoria && filters.categoria.length > 0) {
        filteredVehicles = filteredVehicles.filter(v => 
          filters.categoria!.includes(v.categoria)
        );
      }

      // Apply group filter
      if (filters.gruppo && filters.gruppo.length > 0) {
        filteredVehicles = filteredVehicles.filter(v => 
          filters.gruppo!.includes(v.gruppo)
        );
      }

      // Apply property filter
      if (filters.proprieta && filters.proprieta.length > 0) {
        filteredVehicles = filteredVehicles.filter(v => 
          filters.proprieta!.includes(v.proprieta)
        );
      }

      // Apply location filter
      if (filters.parcoSede && filters.parcoSede.length > 0) {
        filteredVehicles = filteredVehicles.filter(v => 
          filters.parcoSede!.includes(v.parcoSede)
        );
      }

      // Apply insurance filter
      if (filters.assicurazione && filters.assicurazione.length > 0) {
        filteredVehicles = filteredVehicles.filter(v => 
          filters.assicurazione!.includes(v.assicurazione)
        );
      }

      // Apply date filter
      if (filters.dateFilter) {
        const { dateField, startDate, endDate } = filters.dateFilter;
        
        filteredVehicles = filteredVehicles.filter(vehicle => {
          const targetDate = dateField === 'DATA_FINE' ? vehicle.dataFine : vehicle.dataEffettiva;
          
          if (!targetDate) return true; // Include vehicles with null dates
          
          if (startDate && targetDate < startDate) return false;
          if (endDate && targetDate > endDate) return false;
          
          return true;
        });
      }
    }

    return filteredVehicles;
  }

  calculateKPIs(filters?: FilterOptions): FleetKPIs {
    const vehicles = this.getVehicles(filters);
    const activeVehicles = vehicles.filter(v => v.stato === 'Attivo');
    
    const totalMonthlyCost = activeVehicles.reduce((sum, v) => sum + v.canoneImponibile, 0);
    const totalMonthlyCostWithVAT = activeVehicles.reduce((sum, v) => sum + v.canoneIvato, 0);
    const vehiclesWithInsurance = vehicles.filter(v => v.assicurazione && v.assicurazione !== '').length;

    return {
      totalVehicles: vehicles.length,
      activeVehicles: activeVehicles.length,
      totalMonthlyCost,
      totalMonthlyCostWithVAT,
      averageCostPerVehicle: activeVehicles.length > 0 ? totalMonthlyCost / activeVehicles.length : 0,
      utilizationRate: vehicles.length > 0 ? (activeVehicles.length / vehicles.length) * 100 : 0,
      vehiclesWithInsurance,
      insuranceRate: vehicles.length > 0 ? (vehiclesWithInsurance / vehicles.length) * 100 : 0,
    };
  }

  calculateCostSavings(filters?: FilterOptions): CostSavingsAnalysis {
    const vehicles = this.getVehicles(filters);
    const activeVehicles = vehicles.filter(v => 
      v.stato === 'Attivo' && 
      v.dataEffettiva && 
      v.dataFine
    );

    // Find early returns (delivery date < end date)
    const earlyReturns = activeVehicles.filter(v => 
      v.dataEffettiva! < v.dataFine
    );

    const earlyReturnDetails: EarlyReturnDetail[] = earlyReturns.map(vehicle => {
      const daysSaved = Math.floor(
        (vehicle.dataFine.getTime() - vehicle.dataEffettiva!.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      // Calculate daily cost (monthly cost / average days in month)
      const avgDaysPerMonth = 30.44;
      const dailyCost = vehicle.canoneImponibile / avgDaysPerMonth;
      const savingsPerVehicle = daysSaved * dailyCost;

      return {
        targa: vehicle.targa,
        modello: vehicle.modello,
        dataFine: vehicle.dataFine,
        dataEffettiva: vehicle.dataEffettiva!,
        daysSaved,
        canoneImponibile: vehicle.canoneImponibile,
        dailyCost,
        savingsPerVehicle,
      };
    });

    const totalSavings = earlyReturnDetails.reduce((sum, detail) => sum + detail.savingsPerVehicle, 0);
    const totalSavingsWithVAT = totalSavings * 1.22; // Assuming 22% VAT
    const totalDaysSaved = earlyReturnDetails.reduce((sum, detail) => sum + detail.daysSaved, 0);

    return {
      totalSavings,
      totalSavingsWithVAT,
      totalDaysSaved,
      vehiclesWithEarlyReturns: earlyReturns.length,
      averageSavingsPerVehicle: earlyReturns.length > 0 ? totalSavings / earlyReturns.length : 0,
      averageDaysSavedPerVehicle: earlyReturns.length > 0 ? totalDaysSaved / earlyReturns.length : 0,
      earlyReturnDetails,
    };
  }

  getUniqueValues(field: keyof FleetVehicle): string[] {
    const values = this.vehicles
      .map(vehicle => vehicle[field])
      .filter((value, index, array) => 
        value !== null && 
        value !== undefined && 
        value !== '' && 
        array.indexOf(value) === index
      ) as string[];
    
    return values.sort();
  }

  exportToCSV(filters?: FilterOptions): string {
    const vehicles = this.getVehicles(filters);
    
    const csvData = vehicles.map(vehicle => ({
      TARGA: vehicle.targa,
      MODELLO: vehicle.modello,
      CATEGORIA: vehicle.categoria,
      GRUPPO: vehicle.gruppo,
      STATO: vehicle.stato,
      'PROPRIETÀ': vehicle.proprieta,
      'PARCO/SEDE': vehicle.parcoSede,
      'DATA INIZIO': vehicle.dataInizio?.toLocaleDateString('it-IT'),
      'DATA FINE': vehicle.dataFine?.toLocaleDateString('it-IT'),
      'DATA EFFETTIVA': vehicle.dataEffettiva?.toLocaleDateString('it-IT'),
      RICONSEGNA: vehicle.riconsegna?.toLocaleDateString('it-IT'),
      'CANONE IMPONIBILE': vehicle.canoneImponibile,
      'CANONE IVATO': vehicle.canoneIvato,
      ASSICURAZIONE: vehicle.assicurazione,
    }));

    return Papa.unparse(csvData);
  }

  // Chart data generation methods
  getFleetCompositionData(filters?: FilterOptions): Array<{categoria: string, count: number, percentage: number, color: string}> {
    const vehicles = this.getVehicles(filters);
    const categoryCount = new Map<string, number>();

    vehicles.forEach(vehicle => {
      const count = categoryCount.get(vehicle.categoria) || 0;
      categoryCount.set(vehicle.categoria, count + 1);
    });

    const total = vehicles.length;
    const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff7f', '#dc143c', '#00bfff', '#ff69b4', '#32cd32', '#ff4500'];

    return Array.from(categoryCount.entries()).map(([categoria, count], index) => ({
      categoria,
      count,
      percentage: total > 0 ? (count / total) * 100 : 0,
      color: colors[index % colors.length]
    }));
  }

  getCostAnalysisData(filters?: FilterOptions): Array<{categoria: string, count: number, totalCost: number, averageCost: number}> {
    const vehicles = this.getVehicles(filters);
    const categoryData = new Map<string, {count: number, totalCost: number}>();

    vehicles.forEach(vehicle => {
      const existing = categoryData.get(vehicle.categoria) || {count: 0, totalCost: 0};
      categoryData.set(vehicle.categoria, {
        count: existing.count + 1,
        totalCost: existing.totalCost + vehicle.canoneImponibile
      });
    });

    return Array.from(categoryData.entries()).map(([categoria, data]) => ({
      categoria,
      count: data.count,
      totalCost: data.totalCost,
      averageCost: data.count > 0 ? data.totalCost / data.count : 0
    }));
  }

  getSavingsAnalysisData(filters?: FilterOptions): Array<{category: string, vehicleCount: number, totalSavings: number, averageSavings: number, daysSaved: number}> {
    const vehicles = this.getVehicles(filters);
    const categoryData = new Map<string, {vehicles: FleetVehicle[], savings: number, days: number}>();

    vehicles.forEach(vehicle => {
      if (vehicle.dataEffettiva && vehicle.dataFine && vehicle.dataEffettiva < vehicle.dataFine) {
        const existing = categoryData.get(vehicle.categoria) || {vehicles: [], savings: 0, days: 0};
        const daysSaved = Math.ceil((vehicle.dataFine.getTime() - vehicle.dataEffettiva.getTime()) / (1000 * 60 * 60 * 24));
        const daysInMonth = 30;
        const savings = (vehicle.canoneImponibile / daysInMonth) * daysSaved;

        categoryData.set(vehicle.categoria, {
          vehicles: [...existing.vehicles, vehicle],
          savings: existing.savings + savings,
          days: existing.days + daysSaved
        });
      }
    });

    return Array.from(categoryData.entries()).map(([category, data]) => ({
      category,
      vehicleCount: data.vehicles.length,
      totalSavings: data.savings,
      averageSavings: data.vehicles.length > 0 ? data.savings / data.vehicles.length : 0,
      daysSaved: data.days
    }));
  }

  getLocationDistributionData(filters?: FilterOptions): Array<{location: string, count: number, percentage: number}> {
    const vehicles = this.getVehicles(filters);
    const locationCount = new Map<string, number>();

    vehicles.forEach(vehicle => {
      const count = locationCount.get(vehicle.parcoSede) || 0;
      locationCount.set(vehicle.parcoSede, count + 1);
    });

    const total = vehicles.length;

    return Array.from(locationCount.entries()).map(([location, count]) => ({
      location,
      count,
      percentage: total > 0 ? (count / total) * 100 : 0
    }));
  }

  getContractExpirationData(filters?: FilterOptions): Array<{month: string, expiring: number, total: number}> {
    const vehicles = this.getVehicles(filters);
    const monthData = new Map<string, {expiring: number, total: number}>();

    const now = new Date();
    const next12Months = Array.from({length: 12}, (_, i) => {
      const date = new Date(now.getFullYear(), now.getMonth() + i, 1);
      return {
        key: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`,
        label: date.toLocaleDateString('it-IT', { month: 'short', year: 'numeric' })
      };
    });

    next12Months.forEach(month => {
      monthData.set(month.key, {expiring: 0, total: vehicles.length});
    });

    vehicles.forEach(vehicle => {
      const expirationDate = vehicle.dataFine;
      const monthKey = `${expirationDate.getFullYear()}-${String(expirationDate.getMonth() + 1).padStart(2, '0')}`;

      if (monthData.has(monthKey)) {
        const existing = monthData.get(monthKey)!;
        monthData.set(monthKey, {
          ...existing,
          expiring: existing.expiring + 1
        });
      }
    });

    return next12Months.map(month => ({
      month: month.label,
      expiring: monthData.get(month.key)?.expiring || 0,
      total: monthData.get(month.key)?.total || 0
    }));
  }

  getOwnerDistributionData(filters?: FilterOptions): Array<{proprieta: string, vehicleCount: number, totalCost: number, percentage: number}> {
    const vehicles = this.getVehicles(filters);
    const ownerData = new Map<string, {count: number, totalCost: number}>();

    vehicles.forEach(vehicle => {
      const existing = ownerData.get(vehicle.proprieta) || {count: 0, totalCost: 0};
      ownerData.set(vehicle.proprieta, {
        count: existing.count + 1,
        totalCost: existing.totalCost + vehicle.canoneImponibile
      });
    });

    const total = vehicles.length;

    return Array.from(ownerData.entries())
      .map(([proprieta, data]) => ({
        proprieta,
        vehicleCount: data.count,
        totalCost: data.totalCost,
        percentage: total > 0 ? (data.count / total) * 100 : 0
      }))
      .sort((a, b) => b.vehicleCount - a.vehicleCount);
  }
}

// Export singleton instance
export const fleetDataService = new FleetDataService();
export default fleetDataService;
