// AI Chat Service - Integration with Ollama and fleet data insights
import { FleetInsightRequest, FleetInsightResponse, AICapability } from '@/types/fleet';
import enhancedFleetDataService from './enhancedFleetDataService';
import { defaultAIConfig, promptTemplates, systemMessages } from '@/config/ai.config';

interface OllamaConfig {
  endpoint: string;
  model: string;
  temperature: number;
  enabled: boolean;
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  context?: string;
}

interface ConversationContext {
  sessionId: string;
  messages: ChatMessage[];
  fleetSummary?: any;
  lastUpdated: Date;
}

class AIChatService {
  private config: OllamaConfig = {
    endpoint: defaultAIConfig.ollama.endpoint,
    model: defaultAIConfig.ollama.model,
    temperature: defaultAIConfig.ollama.temperature,
    enabled: defaultAIConfig.ollama.enabled
  };
  
  private conversation: ConversationContext | null = null;
  private capabilities: AICapability[] = [
    {
      id: 'fleet-analysis',
      name: 'Fleet Analysis',
      description: 'Analyze fleet composition, utilization, and performance metrics',
      category: 'analysis',
      enabled: true
    },
    {
      id: 'cost-optimization',
      name: 'Cost Optimization',
      description: 'Identify cost reduction opportunities and savings potential',
      category: 'optimization',
      enabled: true
    },
    {
      id: 'predictive-maintenance',
      name: 'Predictive Insights',
      description: 'Predict maintenance needs and contract expirations',
      category: 'prediction',
      enabled: true
    },
    {
      id: 'recommendations',
      name: 'Strategic Recommendations',
      description: 'Provide actionable recommendations for fleet management',
      category: 'recommendation',
      enabled: true
    }
  ];

  async initialize(config?: Partial<OllamaConfig>): Promise<boolean> {
    if (config) {
      this.config = { ...this.config, ...config };
    }

    if (!this.config.enabled) {
      console.log('AI Chat Service: Disabled in configuration');
      return false;
    }

    try {
      // Test connection to Ollama
      const response = await fetch(`${this.config.endpoint}/api/tags`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Ollama connection failed: ${response.status}`);
      }

      const models = await response.json();
      console.log('AI Chat Service: Connected to Ollama', models);
      
      // Initialize conversation context
      this.conversation = {
        sessionId: this.generateSessionId(),
        messages: [],
        lastUpdated: new Date()
      };

      return true;
    } catch (error) {
      console.error('AI Chat Service: Failed to connect to Ollama', error);
      this.config.enabled = false;
      return false;
    }
  }

  async sendMessage(message: string, context?: string): Promise<FleetInsightResponse> {
    if (!this.config.enabled || !this.conversation) {
      return this.createFallbackResponse(message);
    }

    try {
      // Add user message to conversation
      const userMessage: ChatMessage = {
        id: this.generateMessageId(),
        role: 'user',
        content: message,
        timestamp: new Date(),
        context
      };
      
      this.conversation.messages.push(userMessage);

      // Get current fleet data for context
      const fleetInsights = enhancedFleetDataService.getFleetInsights();
      
      // Prepare context for AI
      const systemPrompt = this.buildSystemPrompt(fleetInsights, context);
      const conversationHistory = this.buildConversationHistory();

      // Call Ollama API
      const response = await this.callOllama(systemPrompt, conversationHistory, message);
      
      // Parse AI response
      const aiResponse = this.parseAIResponse(response);
      
      // Add AI message to conversation
      const assistantMessage: ChatMessage = {
        id: this.generateMessageId(),
        role: 'assistant',
        content: aiResponse.answer,
        timestamp: new Date(),
        context
      };
      
      this.conversation.messages.push(assistantMessage);
      this.conversation.lastUpdated = new Date();

      return aiResponse;
    } catch (error) {
      console.error('AI Chat Service: Error processing message', error);
      return this.createErrorResponse(message, error);
    }
  }

  private buildSystemPrompt(fleetInsights: any, context?: string): string {
    const currentDate = new Date().toLocaleDateString('it-IT');
    
    return `You are an expert fleet management AI assistant specializing in Italian vehicle fleet analysis. 
    
Current date: ${currentDate}

Fleet Data Summary:
- Total Vehicles: ${fleetInsights.summary.totalVehicles?.toLocaleString() || 'N/A'}
- Utilization Rate: ${fleetInsights.summary.utilizationRate?.toFixed(1) || 'N/A'}%
- Monthly Costs: €${fleetInsights.summary.totalMonthlyCost?.toLocaleString() || 'N/A'}
- Cost Savings: €${fleetInsights.summary.costSavings?.toLocaleString() || 'N/A'}

Top Vehicle Categories: ${fleetInsights.trends?.topCategories?.map((c: any) => `${c.categoria} (${c.count})`).join(', ') || 'N/A'}
Top Locations: ${fleetInsights.trends?.topLocations?.map((l: any) => `${l.location} (${l.vehicleCount} vehicles)`).join(', ') || 'N/A'}
Expiring Contracts: ${fleetInsights.trends?.expiringContracts || 0} contracts expiring soon

Context: ${context || 'general fleet management'}

Instructions:
1. Provide answers in Italian (you are serving an Italian fleet management company)
2. Use the fleet data provided to give specific, data-driven insights
3. Focus on actionable recommendations
4. Include relevant metrics and numbers when possible
5. If asked about cost optimization, reference the actual savings data
6. For maintenance questions, consider contract expiration dates
7. Always provide practical, business-focused advice
8. Format responses clearly with bullet points when appropriate
9. Reference specific vehicle categories, locations, or metrics when relevant
10. If the data is insufficient for a specific question, acknowledge this and suggest alternative approaches

Remember: You have access to real fleet data with over 1 million vehicle records. Use this to provide specific, valuable insights.`;
  }

  private buildConversationHistory(): string {
    if (!this.conversation || this.conversation.messages.length === 0) {
      return '';
    }

    // Get last 5 messages for context
    const recentMessages = this.conversation.messages.slice(-5);
    
    return recentMessages.map(msg => {
      const role = msg.role === 'user' ? 'Utente' : 'Assistente';
      return `${role}: ${msg.content}`;
    }).join('\n\n');
  }

  private async callOllama(systemPrompt: string, conversationHistory: string, userMessage: string): Promise<string> {
    const prompt = `${systemPrompt}

${conversationHistory ? `Conversazione precedente:\n${conversationHistory}\n\n` : ''}

Domanda attuale: ${userMessage}

Risposta (in italiano, specifica e basata sui dati):`;

    const response = await fetch(`${this.config.endpoint}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.config.model,
        prompt: prompt,
        temperature: this.config.temperature,
        stream: false,
        options: {
          num_predict: 500,
          temperature: this.config.temperature,
          top_k: 40,
          top_p: 0.9,
        }
      }),
    });

    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    return result.response || 'Spiacente, non sono riuscito a generare una risposta.';
  }

  private parseAIResponse(aiResponse: string): FleetInsightResponse {
    // Extract insights and recommendations from AI response
    const insights: string[] = [];
    const recommendations: string[] = [];
    const dataReferences: string[] = [];

    // Simple parsing - could be enhanced with more sophisticated NLP
    const lines = aiResponse.split('\n').filter(line => line.trim());
    
    lines.forEach(line => {
      if (line.includes('€') || line.includes('%') || /\d+/.test(line)) {
        dataReferences.push(line.trim());
      }
      if (line.includes('raccomando') || line.includes('suggerisco') || line.includes('dovresti')) {
        recommendations.push(line.trim());
      }
      if (line.includes('dato') || line.includes('analisi') || line.includes('tendenza')) {
        insights.push(line.trim());
      }
    });

    return {
      answer: aiResponse,
      insights: insights.slice(0, 3), // Limit to top 3 insights
      recommendations: recommendations.slice(0, 3), // Limit to top 3 recommendations
      dataReferences: dataReferences.slice(0, 5), // Limit to top 5 data points
      confidence: 0.8 // Could be calculated based on response quality
    };
  }

  private createFallbackResponse(message: string): FleetInsightResponse {
    // Provide intelligent fallback responses based on keywords
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('costo') || lowerMessage.includes('spesa')) {
      const fleetInsights = enhancedFleetDataService.getFleetInsights();
      return {
        answer: `Basandomi sui dati della flotta:\n\n• Costi mensili totali: €${fleetInsights.summary.totalMonthlyCost?.toLocaleString() || 'N/A'}\n• Risparmi ottenuti: €${fleetInsights.summary.costSavings?.toLocaleString() || 'N/A'}\n• Tasso di utilizzo: ${fleetInsights.summary.utilizationRate?.toFixed(1) || 'N/A'}%\n\nPer analisi più dettagliate, configura l'integrazione AI con Ollama.`,
        insights: ['I dati mostrano opportunità di ottimizzazione dei costi'],
        recommendations: ['Considera di abilitare l\'AI per raccomandazioni personalizzate'],
        dataReferences: [`${fleetInsights.summary.totalVehicles} veicoli totali`],
        confidence: 0.6
      };
    }

    if (lowerMessage.includes('veicol') || lowerMessage.includes('flotta')) {
      const fleetInsights = enhancedFleetDataService.getFleetInsights();
      return {
        answer: `Panoramica della flotta:\n\n• Veicoli totali: ${fleetInsights.summary.totalVehicles?.toLocaleString() || 'N/A'}\n• Tasso di utilizzo: ${fleetInsights.summary.utilizationRate?.toFixed(1) || 'N/A'}%\n• Top categorie: ${fleetInsights.trends?.topCategories?.slice(0, 3).map((c: any) => c.categoria).join(', ') || 'N/A'}\n\nPer approfondimenti e raccomandazioni personalizzate, abilita l'integrazione AI.`,
        insights: ['La flotta mostra buone metriche di performance'],
        recommendations: ['Abilita l\'AI per analisi predittive'],
        dataReferences: [`${fleetInsights.summary.totalVehicles} veicoli`],
        confidence: 0.7
      };
    }

    return {
      answer: 'L\'AI è attualmente disabilitato. Per ricevere insights intelligenti sulla tua flotta, configura l\'integrazione con Ollama nelle impostazioni.\n\nNel frattempo, puoi:\n• Esplorare i dati nelle altre sezioni\n• Utilizzare i filtri avanzati\n• Consultare i dashboard analitici',
      insights: ['L\'AI non è configurato'],
      recommendations: ['Configura Ollama per abilitare l\'AI', 'Esplora i dashboard per insights manuali'],
      dataReferences: [],
      confidence: 0.3
    };
  }

  private createErrorResponse(message: string, error: any): FleetInsightResponse {
    return {
      answer: `Spiacente, si è verificato un errore durante l'elaborazione della tua richiesta.\n\nDettagli tecnici: ${error.message || 'Errore sconosciuto'}\n\nSuggerimenti:\n• Verifica che Ollama sia in esecuzione su ${this.config.endpoint}\n• Controlla la connessione di rete\n• Prova a ricaricare la pagina`,
      insights: ['Errore di connessione AI'],
      recommendations: ['Verifica la configurazione di Ollama', 'Controlla i log per dettagli'],
      dataReferences: [],
      confidence: 0.1
    };
  }

  getAvailableModels(): string[] {
    // Common Ollama models for fleet management
    return [
      'llama2', // General purpose, good for analysis
      'llama2:13b', // Larger model, better for complex reasoning
      'mistral', // Good for technical domains
      'codellama', // Good for data analysis
      'neural-chat', // Optimized for conversations
      'openchat', // Good for business contexts
    ];
  }

  getModelRecommendation(): string {
    return 'mistral'; // Recommended for business/fleet management context
  }

  updateConfig(newConfig: Partial<OllamaConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  getConfig(): OllamaConfig {
    return { ...this.config };
  }

  getCapabilities(): AICapability[] {
    return [...this.capabilities];
  }

  getConversationHistory(): ChatMessage[] {
    return this.conversation?.messages || [];
  }

  clearConversation(): void {
    if (this.conversation) {
      this.conversation.messages = [];
      this.conversation.lastUpdated = new Date();
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Check if Ollama is available and running
  async checkOllamaStatus(): Promise<{available: boolean, models?: string[], error?: string}> {
    try {
      const response = await fetch(`${this.config.endpoint}/api/tags`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        return {
          available: false,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }

      const data = await response.json();
      const models = data.models?.map((m: any) => m.name) || [];
      
      return {
        available: true,
        models
      };
    } catch (error) {
      return {
        available: false,
        error: error instanceof Error ? error.message : 'Connection failed'
      };
    }
  }
}

export const aiChatService = new AIChatService();
export default aiChatService;