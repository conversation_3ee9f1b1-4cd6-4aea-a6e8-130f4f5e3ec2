// Mock data service for generating realistic fleet management data

export interface TimelineEvent {
  id: string;
  vehicleId: string;
  vehicleName: string;
  type: 'maintenance' | 'service' | 'inspection' | 'repair';
  title: string;
  startDate: Date;
  endDate: Date;
  status: 'scheduled' | 'in-progress' | 'completed' | 'overdue';
  priority: 'low' | 'medium' | 'high' | 'critical';
  cost?: number;
}

export interface WaterfallData {
  category: string;
  value: number;
  type: 'positive' | 'negative' | 'total';
  cumulative: number;
}

export interface FleetMetrics {
  totalVehicles: number;
  activeVehicles: number;
  utilizationRate: number;
  avgCostPerVehicle: number;
  maintenanceCosts: number;
  fuelCosts: number;
  operationalCosts: number;
}

export interface UtilizationData {
  vehicleId: string;
  vehicleName: string;
  type: string;
  utilizationRate: number;
  milesThisMonth: number;
  lastService: Date;
  nextService: Date;
}

class MockDataService {
  private vehicleTypes = ['Sedan', 'SUV', 'Van', 'Truck', 'Pickup'];
  private maintenanceTypes = ['Oil Change', 'Tire Rotation', 'Brake Service', 'Engine Service', 'Transmission'];
  
  generateTimelineData(days: number = 30): TimelineEvent[] {
    const events: TimelineEvent[] = [];
    const startDate = new Date();
    
    for (let i = 0; i < 50; i++) {
      const vehicleId = `VEH-${String(i + 1).padStart(4, '0')}`;
      const eventDate = new Date(startDate.getTime() + Math.random() * days * 24 * 60 * 60 * 1000);
      const duration = Math.floor(Math.random() * 4) + 1; // 1-4 hours
      
      events.push({
        id: `event-${i}`,
        vehicleId,
        vehicleName: `${this.vehicleTypes[Math.floor(Math.random() * this.vehicleTypes.length)]} ${vehicleId}`,
        type: ['maintenance', 'service', 'inspection', 'repair'][Math.floor(Math.random() * 4)] as any,
        title: this.maintenanceTypes[Math.floor(Math.random() * this.maintenanceTypes.length)],
        startDate: eventDate,
        endDate: new Date(eventDate.getTime() + duration * 60 * 60 * 1000),
        status: ['scheduled', 'in-progress', 'completed', 'overdue'][Math.floor(Math.random() * 4)] as any,
        priority: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
        cost: Math.floor(Math.random() * 1000) + 100
      });
    }
    
    return events.sort((a, b) => a.startDate.getTime() - b.startDate.getTime());
  }

  generateWaterfallData(): WaterfallData[] {
    const baseValue = 1240000; // Starting budget
    
    return [
      { category: 'Initial Budget', value: baseValue, type: 'total', cumulative: baseValue },
      { category: 'Fuel Costs', value: -320000, type: 'negative', cumulative: baseValue - 320000 },
      { category: 'Maintenance', value: -180000, type: 'negative', cumulative: baseValue - 500000 },
      { category: 'Insurance', value: -95000, type: 'negative', cumulative: baseValue - 595000 },
      { category: 'Depreciation', value: -210000, type: 'negative', cumulative: baseValue - 805000 },
      { category: 'Efficiency Gains', value: 45000, type: 'positive', cumulative: baseValue - 760000 },
      { category: 'Contract Savings', value: 28000, type: 'positive', cumulative: baseValue - 732000 },
      { category: 'Final Position', value: baseValue - 732000, type: 'total', cumulative: baseValue - 732000 }
    ];
  }

  generateFleetMetrics(): FleetMetrics {
    return {
      totalVehicles: 2847,
      activeVehicles: 2734,
      utilizationRate: 87.3,
      avgCostPerVehicle: 435,
      maintenanceCosts: 324500,
      fuelCosts: 890200,
      operationalCosts: 1240000
    };
  }

  generateUtilizationData(): UtilizationData[] {
    const data: UtilizationData[] = [];
    
    for (let i = 0; i < 100; i++) {
      const vehicleId = `VEH-${String(i + 1).padStart(4, '0')}`;
      const lastService = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000);
      
      data.push({
        vehicleId,
        vehicleName: `${this.vehicleTypes[Math.floor(Math.random() * this.vehicleTypes.length)]} ${vehicleId}`,
        type: this.vehicleTypes[Math.floor(Math.random() * this.vehicleTypes.length)],
        utilizationRate: Math.floor(Math.random() * 40) + 60,
        milesThisMonth: Math.floor(Math.random() * 3000) + 500,
        lastService,
        nextService: new Date(lastService.getTime() + 90 * 24 * 60 * 60 * 1000)
      });
    }
    
    return data.sort((a, b) => b.utilizationRate - a.utilizationRate);
  }

  generateCostBreakdownData() {
    return [
      { category: 'Fuel', value: 890200, percentage: 42.3, color: 'hsl(var(--chart-1))' },
      { category: 'Maintenance', value: 324500, percentage: 15.4, color: 'hsl(var(--chart-2))' },
      { category: 'Insurance', value: 267800, percentage: 12.7, color: 'hsl(var(--chart-3))' },
      { category: 'Depreciation', value: 445600, percentage: 21.2, color: 'hsl(var(--chart-4))' },
      { category: 'Other', value: 175900, percentage: 8.4, color: 'hsl(var(--chart-5))' }
    ];
  }

  generateMonthlyTrends() {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months.map(month => ({
      month,
      costs: Math.floor(Math.random() * 200000) + 900000,
      utilization: Math.floor(Math.random() * 20) + 75,
      maintenance: Math.floor(Math.random() * 50000) + 250000,
      fuel: Math.floor(Math.random() * 100000) + 800000
    }));
  }
}

export const mockDataService = new MockDataService();