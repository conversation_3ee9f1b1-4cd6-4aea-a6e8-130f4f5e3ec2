import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type DashboardRole = 'manager' | 'finance' | 'operations';

export interface Widget {
  id: string;
  type: 'timeline' | 'waterfall' | 'utilization' | 'cost-breakdown' | 'maintenance' | 'locations' | 'fleet-composition' | 'cost-analysis' | 'location-distribution' | 'contract-expiration' | 'savings-analysis' | 'owner-distribution';
  title: string;
  size: 'small' | 'medium' | 'large';
  position: { x: number; y: number };
  config?: Record<string, any>;
}

export interface DashboardLayout {
  id: string;
  name: string;
  role: DashboardRole;
  widgets: Widget[];
}

interface DashboardState {
  // Current state
  currentRole: DashboardRole;
  currentLayout: DashboardLayout;
  widgets: Widget[];
  
  // Filters
  activeFilters: Record<string, any>;
  crossFilterEnabled: boolean;
  
  // Actions
  setRole: (role: DashboardRole) => void;
  addWidget: (widget: Omit<Widget, 'id'>) => void;
  removeWidget: (widgetId: string) => void;
  updateWidget: (widgetId: string, updates: Partial<Widget>) => void;
  moveWidget: (widgetId: string, position: { x: number; y: number }) => void;
  saveLayout: (name: string) => void;
  loadLayout: (layoutId: string) => void;
  setFilters: (filters: Record<string, any>) => void;
  clearFilters: () => void;
  toggleCrossFilter: () => void;
}

// Default layouts for each role
const defaultLayouts: Record<DashboardRole, DashboardLayout> = {
  manager: {
    id: 'manager-default',
    name: 'Manager Dashboard',
    role: 'manager',
    widgets: [
      {
        id: 'manager-composition-1',
        type: 'utilization',
        title: 'Fleet Composition',
        size: 'medium',
        position: { x: 0, y: 0 }
      },
      {
        id: 'manager-cost-1',
        type: 'cost-breakdown',
        title: 'Cost Analysis by Category',
        size: 'medium',
        position: { x: 1, y: 0 }
      },
      {
        id: 'manager-location-1',
        type: 'locations',
        title: 'Location Distribution',
        size: 'medium',
        position: { x: 2, y: 0 }
      }
    ]
  },
  finance: {
    id: 'finance-default',
    name: 'Finance Dashboard',
    role: 'finance',
    widgets: [
      {
        id: 'finance-cost-1',
        type: 'cost-breakdown',
        title: 'Cost Analysis by Category',
        size: 'large',
        position: { x: 0, y: 0 }
      },
      {
        id: 'finance-savings-1',
        type: 'maintenance',
        title: 'Savings Analysis',
        size: 'medium',
        position: { x: 2, y: 0 }
      },
      {
        id: 'finance-composition-1',
        type: 'utilization',
        title: 'Fleet Composition',
        size: 'medium',
        position: { x: 0, y: 1 }
      }
    ]
  },
  operations: {
    id: 'operations-default',
    name: 'Operations Dashboard',
    role: 'operations',
    widgets: [
      {
        id: 'ops-contracts-1',
        type: 'timeline',
        title: 'Contract Expiration Timeline',
        size: 'large',
        position: { x: 0, y: 0 }
      },
      {
        id: 'ops-locations-1',
        type: 'locations',
        title: 'Location Distribution',
        size: 'medium',
        position: { x: 2, y: 0 }
      },
      {
        id: 'ops-composition-1',
        type: 'utilization',
        title: 'Fleet Composition',
        size: 'medium',
        position: { x: 0, y: 1 }
      }
    ]
  }
};

export const useDashboardStore = create<DashboardState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentRole: 'manager',
      currentLayout: defaultLayouts.manager,
      widgets: defaultLayouts.manager.widgets,
      activeFilters: {},
      crossFilterEnabled: true,

      // Actions
      setRole: (role) => {
        const layout = defaultLayouts[role];
        set({
          currentRole: role,
          currentLayout: layout,
          widgets: layout.widgets
        });
      },

      addWidget: (widget) => {
        const newWidget: Widget = {
          ...widget,
          id: `widget-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        };
        set((state) => ({
          widgets: [...state.widgets, newWidget]
        }));
      },

      removeWidget: (widgetId) => {
        set((state) => ({
          widgets: state.widgets.filter(w => w.id !== widgetId)
        }));
      },

      updateWidget: (widgetId, updates) => {
        set((state) => ({
          widgets: state.widgets.map(w => 
            w.id === widgetId ? { ...w, ...updates } : w
          )
        }));
      },

      moveWidget: (widgetId, position) => {
        set((state) => ({
          widgets: state.widgets.map(w => 
            w.id === widgetId ? { ...w, position } : w
          )
        }));
      },

      saveLayout: (name) => {
        const { currentRole, widgets } = get();
        const layout: DashboardLayout = {
          id: `custom-${Date.now()}`,
          name,
          role: currentRole,
          widgets: [...widgets]
        };
        set({ currentLayout: layout });
      },

      loadLayout: (layoutId) => {
        // In a real app, this would load from saved layouts
        const role = get().currentRole;
        const layout = defaultLayouts[role];
        set({
          currentLayout: layout,
          widgets: layout.widgets
        });
      },

      setFilters: (filters) => {
        set((state) => ({
          activeFilters: { ...state.activeFilters, ...filters }
        }));
      },

      clearFilters: () => {
        set({ activeFilters: {} });
      },

      toggleCrossFilter: () => {
        set((state) => ({
          crossFilterEnabled: !state.crossFilterEnabled
        }));
      }
    }),
    {
      name: 'dashboard-store',
      partialize: (state) => ({
        currentRole: state.currentRole,
        widgets: state.widgets,
        activeFilters: state.activeFilters,
        crossFilterEnabled: state.crossFilterEnabled
      })
    }
  )
);