// Configurazione AI per Fleet Sight Dashboard

export interface AIConfig {
  ollama: {
    endpoint: string;
    model: string;
    temperature: number;
    enabled: boolean;
    maxTokens: number;
    timeout: number;
  };
  features: {
    fleetAnalysis: boolean;
    costOptimization: boolean;
    predictiveInsights: boolean;
    recommendations: boolean;
  };
  ui: {
    showQuickPrompts: boolean;
    autoInitialize: boolean;
    showTypingIndicator: boolean;
    maxMessages: number;
  };
}

export const defaultAIConfig: AIConfig = {
  ollama: {
    endpoint: 'http://localhost:11434',
    model: 'llama3.2:latest',
    temperature: 0.7,
    enabled: true,
    maxTokens: 500,
    timeout: 30000, // 30 secondi
  },
  features: {
    fleetAnalysis: true,
    costOptimization: true,
    predictiveInsights: true,
    recommendations: true,
  },
  ui: {
    showQuickPrompts: true,
    autoInitialize: true,
    showTypingIndicator: true,
    maxMessages: 50,
  },
};

// Prompt templates per diversi contesti
export const promptTemplates = {
  fleetAnalysis: `Sei un esperto analista di flotte aziendali italiane. 
Analizza i seguenti dati della flotta e fornisci insights specifici e actionable:

{fleetData}

Concentrati su:
- Metriche chiave di performance
- Opportunità di ottimizzazione
- Trend e pattern significativi
- Raccomandazioni concrete

Rispondi in italiano con un tono professionale ma accessibile.`,

  costOptimization: `Sei un consulente specializzato nell'ottimizzazione dei costi per flotte aziendali italiane.
Analizza i seguenti dati di costo e utilizzo:

{costData}

Fornisci:
- Analisi dei costi principali
- Identificazione di sprechi o inefficienze
- Strategie di riduzione costi
- ROI potenziale delle ottimizzazioni

Rispondi in italiano con focus su risultati misurabili.`,

  predictiveInsights: `Sei un data scientist specializzato in analytics predittive per flotte aziendali.
Basandoti sui dati storici e attuali:

{historicalData}

Fornisci:
- Previsioni sui trend futuri
- Identificazione di rischi potenziali
- Opportunità emergenti
- Raccomandazioni preventive

Rispondi in italiano con supporto di dati quantitativi.`,

  general: `Sei un assistente AI esperto in gestione di flotte aziendali italiane.
Hai accesso ai seguenti dati della flotta:

{fleetContext}

Rispondi alla domanda dell'utente in modo:
- Specifico e basato sui dati
- Professionale ma comprensibile
- Con raccomandazioni actionable
- In italiano

Domanda: {userQuestion}`
};

// Configurazione per diversi modelli Ollama
export const modelConfigs = {
  'llama3.2:latest': {
    temperature: 0.7,
    maxTokens: 500,
    topP: 0.9,
    topK: 40,
    repeatPenalty: 1.1,
  },
  'mistral:latest': {
    temperature: 0.6,
    maxTokens: 600,
    topP: 0.8,
    topK: 50,
    repeatPenalty: 1.0,
  },
  'codellama:latest': {
    temperature: 0.3,
    maxTokens: 800,
    topP: 0.7,
    topK: 30,
    repeatPenalty: 1.2,
  },
};

// Messaggi di sistema per diversi contesti
export const systemMessages = {
  welcome: `🚀 Ciao! Sono il tuo assistente AI per la gestione della flotta.

Posso aiutarti con:
• Analisi delle prestazioni della flotta
• Ottimizzazione dei costi operativi
• Insights sui dati e trend
• Raccomandazioni strategiche
• Previsioni e analytics predittive

Cosa vorresti sapere sulla tua flotta?`,

  error: `⚠️ Si è verificato un errore durante l'elaborazione della tua richiesta.

Dettagli tecnici: {error}

Suggerimenti:
• Verifica che Ollama sia in esecuzione su http://localhost:11434
• Controlla la connessione di rete
• Prova a ricaricare la pagina

Posso comunque fornirti informazioni basic sui dati della flotta.`,

  offline: `🔌 AI non disponibile

Ollama non è configurato o non è in esecuzione.

Per abilitare l'AI completa:
1. Verifica che Ollama sia in esecuzione: \`ollama serve\`
2. Assicurati che il modello sia disponibile: \`ollama pull llama3.2\`
3. Verifica connessione su localhost:11434

Nel frattempo posso fornirti risposte basic sui dati.`,
};

export default defaultAIConfig;
