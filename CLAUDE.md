# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a dual-stack fleet management dashboard project containing:

1. **Streamlit Dashboard** (Primary) - A Python-based fleet cost analysis dashboard
2. **React Command Center** (Secondary) - A modern React-based fleet management interface

Both applications analyze vehicle fleet data, focusing on lease costs, vehicle status, and savings analysis.

## Development Commands

### Streamlit Dashboard (Primary Application)
```bash
# Run the dashboard locally
streamlit run streamlit_app.py

# Install dependencies
pip install -r requirements.txt

# Run with Docker
docker build -t dashboard-flotta .
docker run -p 8501:8501 dashboard-flotta

# Deploy to Render.com (configured)
# Uses render.yaml for automatic deployment
```

### React Command Center (Enhanced Application)
```bash
# Navigate to React app directory
cd fleet-sight-command-center

# Install dependencies
npm install

# Development server (starts on http://localhost:8080)
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint

# Check TypeScript compilation
npx tsc --noEmit
```

### AI Integration Setup
```bash
# Install Ollama (for AI features)
# Visit https://ollama.ai and download for your OS

# Start Ollama with recommended model
ollama run mistral
# OR
ollama run llama2

# Verify Ollama is running
curl http://localhost:11434/api/tags

# List available models
ollama list
```

## Architecture

### Streamlit Dashboard Architecture
- **Main Entry**: `streamlit_app.py` - Core application with tabs for different views
- **Data Layer**: `src/data_handler.py` - FleetDataHandler class for CSV operations, filtering, and calculations
- **Visualization**: `src/charts.py` - ChartGenerator class for Plotly charts
- **Data Source**: `RegistroFLOTTA_Master.csv` - Italian fleet registry data (UTF-8-SIG encoding)

### React Command Center Architecture (Enhanced)
- **Framework**: Vite + React + TypeScript
- **UI Components**: shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS with custom design system
- **State Management**: Zustand for global state, React Query for data fetching
- **Routing**: React Router DOM for navigation
- **Layout**: Sidebar + Header layout with draggable widgets
- **AI Integration**: Ollama service with contextual fleet insights
- **Data Services**: 
  - `fleetDataService.ts` - Original service for basic operations
  - `enhancedFleetDataService.ts` - Advanced analytics and comprehensive data handling
  - `aiChatService.ts` - AI chat integration with Ollama
- **Enhanced Pages**:
  - `EnhancedFleetAnalysis.tsx` - Advanced fleet analytics with 5 detailed tabs
  - `EnhancedData.tsx` - Comprehensive data management with advanced filtering
  - `EnhancedSettings.tsx` - Complete settings management including AI configuration

### Key Data Concepts
- **CANONE IMPONIBILE**: Monthly lease costs (pre-tax) - the primary cost metric
- **CANONE IVATO**: Monthly lease costs (with VAT)
- **Early Returns**: Vehicles returned before contract end date (generates cost savings)
- **Expiration Analysis**: Timeline of future lease endings and cost reductions

## Key Features

### Streamlit Dashboard
- **Global Filtering**: Multi-select filters for categories, groups, status, locations, companies
- **Enhanced Date Filtering**: Flexible date range filtering with special handling for cost savings analysis
- **KPI Dashboard**: Real-time fleet metrics and cost analysis
- **Cost Savings Analysis**: Detailed analysis of savings from early vehicle returns
- **Expiration Impact**: Timeline showing future cost reductions from lease expirations
- **Interactive Data Tables**: AgGrid-based tables with inline editing and export capabilities
- **Charts**: Plotly-based visualizations for fleet analytics

### React Command Center (Enhanced Features)
- **AI-Powered Insights**: Integrated Ollama AI assistant for intelligent fleet analysis
- **Comprehensive Analytics**: Multi-tab fleet analysis with 25+ data fields
- **Advanced Data Management**: Sophisticated filtering, sorting, and export capabilities
- **Interactive Visualizations**: Recharts-based charts with drill-down capabilities
- **Real-time AI Chat**: Contextual AI assistance for fleet optimization
- **Complete Settings Management**: AI configuration, user preferences, and system settings

#### AI Assistant Capabilities
- **Fleet Analysis**: Analyze composition, utilization, and performance metrics
- **Cost Optimization**: Identify savings opportunities and cost reduction strategies
- **Predictive Insights**: Contract expiration analysis and maintenance predictions
- **Strategic Recommendations**: Data-driven recommendations for fleet management
- **Italian Language Support**: Native Italian interface for Italian fleet management
- **Contextual Responses**: AI responses tailored to current page and data context

## Important Implementation Details

### Data Handling
- CSV files use UTF-8-SIG encoding with comma separators
- Date format: MM/DD/YY for all date columns
- Monetary values are stored as floats and displayed with € formatting
- Memory optimizations: Categorical data types, duplicate removal, optimized numeric types

### Performance Considerations
- **Streamlit**: Uses st.cache_data for data loading, AgGrid pagination for large datasets
- **React**: Optimized with React.memo, useMemo, and efficient state management
- **Large Datasets**: Tables limited to 5000 rows in AgGrid to prevent memory issues

### Development Workflow
1. **Data Changes**: Edit CSV files and test with Streamlit dashboard first
2. **UI Changes**: Modify React components in shadcn/ui pattern
3. **Charts**: Use Plotly for Streamlit, Recharts for React
4. **Testing**: No formal test framework - verify functionality through manual testing

## Common Tasks

### Adding New Filters
1. Update `render_sidebar_filters()` in `streamlit_app.py`
2. Modify `apply_filters()` in `data_handler.py`
3. For React: Update `AdvancedFilterOptions` interface in `types/fleet.ts`
4. Add filter logic to `enhancedFleetDataService.ts`

### Adding New Charts
1. **Streamlit**: Add method to `ChartGenerator` class in `charts.py`
2. **React**: Create new chart component using Recharts in enhanced pages
3. Update analytics calculation in `enhancedFleetDataService.ts`

### Modifying KPIs
1. **Streamlit**: Update `calculate_kpis()` in `data_handler.py`
2. **React**: Modify `calculateComprehensiveAnalytics()` in `enhancedFleetDataService.ts`
3. Update interfaces in `types/fleet.ts`

### AI Assistant Customization
1. **Prompts**: Modify system prompts in `aiChatService.ts`
2. **Models**: Add new models to `getAvailableModels()` method
3. **Context**: Enhance `buildSystemPrompt()` for better insights
4. **Languages**: Extend language support in prompt templates

### Deployment
- **Streamlit**: Configured for Render.com deployment via `render.yaml`
- **React**: Can be deployed via Lovable platform or static hosting
- **Docker**: Dockerfile provided for containerized deployment
- **AI Requirements**: Ollama must be accessible at configured endpoint

## File Structure Notes

- `requirements.txt`: Python dependencies for Streamlit app
- `Dockerfile`: Container configuration for Streamlit app
- `render.yaml`: Render.com deployment configuration
- `fleet-sight-command-center/`: Complete React application
- `src/`: Python modules for data handling and charts
- CSV files are excluded from git via `.gitignore`

## Data Privacy
- No sensitive data should be committed to the repository
- CSV files containing actual fleet data are excluded from version control
- Use sample data for development and testing